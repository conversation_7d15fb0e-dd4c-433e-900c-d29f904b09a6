#ifndef __BSP_SYSTEM_H__
#define __BSP_SYSTEM_H__

#include "stdio.h"
#include "stdarg.h"
#include "stdint.h"
#include "string.h"
#include "math.h"

/*bsp*/
#include "ti_msp_dl_config.h"
#include "bsp_usart.h"

/*APP*/
#include "Scheduler.h"
#include "systick.h"
#include "ringbuffer.h"
#include "motor.h"
#include "Ganway.h"
#include "No_Mcu_Ganv_Grayscale_Sensor_Config.h"
#include "encoder.h"
#include "key.h"
#include "oled.h"
#include "hal_adapter.h"  // HAL适配层 - 基于移植文档最佳实践
#include "pid_controller.h"  // PID控制器 - 重新实现被注释的PID功能
#include "enhanced_ganway.h"  // 增强寻迹算法 - 状态机模式替换简单if-else
#endif
