#ifndef ENHANCED_GANWAY_H
#define ENHANCED_GANWAY_H

#include "bsp_system.h"

/**
 * @file enhanced_ganway.h
 * @brief 增强寻迹算法头文件 - 基于状态机设计模式
 * @details 重构简单if-else逻辑为智能状态机，提供更稳定的寻迹策略
 * <AUTHOR>
 */

/**************************** 寻迹状态定义 ****************************/

/**
 * @brief 寻迹状态枚举
 */
typedef enum {
    TRACK_STATE_IDLE = 0,           // 空闲状态
    TRACK_STATE_STRAIGHT,           // 直线行驶
    TRACK_STATE_LEFT_SLIGHT,        // 轻微左转
    TRACK_STATE_RIGHT_SLIGHT,       // 轻微右转
    TRACK_STATE_LEFT_TURN,          // 左转
    TRACK_STATE_RIGHT_TURN,         // 右转
    TRACK_STATE_LEFT_SHARP,         // 急左转
    TRACK_STATE_RIGHT_SHARP,        // 急右转
    TRACK_STATE_LOST_LINE,          // 丢线状态
    TRACK_STATE_STOP,               // 停止状态
    TRACK_STATE_REVERSE,            // 倒车状态
    TRACK_STATE_SEARCH              // 搜索状态
} TrackState_t;

/**
 * @brief 寻迹事件枚举
 */
typedef enum {
    TRACK_EVENT_NONE = 0,           // 无事件
    TRACK_EVENT_LINE_DETECTED,      // 检测到线
    TRACK_EVENT_LINE_LOST,          // 丢失线
    TRACK_EVENT_LEFT_DEVIATION,     // 左偏
    TRACK_EVENT_RIGHT_DEVIATION,    // 右偏
    TRACK_EVENT_STRAIGHT_LINE,      // 直线
    TRACK_EVENT_SHARP_TURN,         // 急转弯
    TRACK_EVENT_INTERSECTION,       // 十字路口
    TRACK_EVENT_STOP_COMMAND,       // 停止命令
    TRACK_EVENT_START_COMMAND       // 启动命令
} TrackEvent_t;

/**************************** 寻迹控制结构体 ****************************/

/**
 * @brief 寻迹控制器结构体
 */
typedef struct {
    // 状态机相关
    TrackState_t current_state;     // 当前状态
    TrackState_t last_state;        // 上一状态
    TrackEvent_t last_event;        // 最后事件
    uint32_t state_enter_time;      // 状态进入时间
    uint32_t state_duration;        // 状态持续时间
    
    // 传感器数据
    uint8_t sensor_data;            // 8位传感器数据
    uint8_t last_sensor_data;       // 上次传感器数据
    uint8_t sensor_history[10];     // 传感器历史数据
    uint8_t history_index;          // 历史数据索引
    
    // 控制参数
    int32_t left_speed;             // 左电机速度
    int32_t right_speed;            // 右电机速度
    int32_t base_speed;             // 基础速度
    int32_t turn_speed_diff;        // 转弯速度差
    
    // 丢线处理
    uint32_t line_lost_time;        // 丢线时间
    uint32_t line_lost_timeout;     // 丢线超时时间
    TrackState_t last_valid_state;  // 最后有效状态
    uint8_t search_direction;       // 搜索方向 (0=左, 1=右)
    
    // 性能统计
    uint32_t state_change_count;    // 状态切换次数
    uint32_t line_lost_count;       // 丢线次数
    uint32_t total_run_time;        // 总运行时间
    
    // 配置参数
    uint8_t enable_adaptive;        // 使能自适应控制
    uint8_t enable_pid;             // 使能PID控制
    uint8_t debug_mode;             // 调试模式
} TrackController_t;

/**************************** 全局变量声明 ****************************/

// 寻迹控制器实例
extern TrackController_t track_controller;

/**************************** 核心寻迹函数 ****************************/

/**
 * @brief 增强寻迹主函数 - 保持与原Way函数接口兼容
 * @param sensor_data 8位传感器数据
 * @note 替换原Ganway.c中的Way函数，内部使用状态机实现
 */
void Way(unsigned char sensor_data);

/**
 * @brief 寻迹控制器初始化
 * @param controller 寻迹控制器指针
 */
void Track_Controller_Init(TrackController_t *controller);

/**
 * @brief 状态机主处理函数
 * @param controller 寻迹控制器指针
 * @param sensor_data 传感器数据
 */
void Track_StateMachine_Process(TrackController_t *controller, uint8_t sensor_data);

/**
 * @brief 传感器数据分析
 * @param sensor_data 8位传感器数据
 * @return 检测到的事件
 */
TrackEvent_t Track_Analyze_Sensor(uint8_t sensor_data);

/**************************** 状态处理函数 ****************************/

/**
 * @brief 直线行驶状态处理
 * @param controller 寻迹控制器指针
 */
void Track_State_Straight(TrackController_t *controller);

/**
 * @brief 左转状态处理
 * @param controller 寻迹控制器指针
 * @param turn_level 转弯级别 (0=轻微, 1=普通, 2=急转)
 */
void Track_State_LeftTurn(TrackController_t *controller, uint8_t turn_level);

/**
 * @brief 右转状态处理
 * @param controller 寻迹控制器指针
 * @param turn_level 转弯级别 (0=轻微, 1=普通, 2=急转)
 */
void Track_State_RightTurn(TrackController_t *controller, uint8_t turn_level);

/**
 * @brief 丢线状态处理
 * @param controller 寻迹控制器指针
 */
void Track_State_LostLine(TrackController_t *controller);

/**
 * @brief 搜索状态处理
 * @param controller 寻迹控制器指针
 */
void Track_State_Search(TrackController_t *controller);

/**
 * @brief 停止状态处理
 * @param controller 寻迹控制器指针
 */
void Track_State_Stop(TrackController_t *controller);

/**************************** 辅助函数 ****************************/

/**
 * @brief 状态切换函数
 * @param controller 寻迹控制器指针
 * @param new_state 新状态
 */
void Track_ChangeState(TrackController_t *controller, TrackState_t new_state);

/**
 * @brief 计算线位置
 * @param sensor_data 传感器数据
 * @return 线位置 (-100到100, 0为中心)
 */
int16_t Track_CalculateLinePosition(uint8_t sensor_data);

/**
 * @brief 传感器数据滤波
 * @param controller 寻迹控制器指针
 * @param sensor_data 原始传感器数据
 * @return 滤波后的传感器数据
 */
uint8_t Track_SensorFilter(TrackController_t *controller, uint8_t sensor_data);

/**
 * @brief 自适应速度控制
 * @param controller 寻迹控制器指针
 * @param line_position 线位置
 */
void Track_AdaptiveSpeedControl(TrackController_t *controller, int16_t line_position);

/**************************** 配置和调试函数 ****************************/

/**
 * @brief 设置基础速度
 * @param speed 基础速度值
 */
void Track_SetBaseSpeed(int32_t speed);

/**
 * @brief 设置转弯速度差
 * @param speed_diff 转弯速度差
 */
void Track_SetTurnSpeedDiff(int32_t speed_diff);

/**
 * @brief 使能自适应控制
 * @param enable 使能标志
 */
void Track_EnableAdaptive(uint8_t enable);

/**
 * @brief 使能PID控制
 * @param enable 使能标志
 */
void Track_EnablePID(uint8_t enable);

/**
 * @brief 获取寻迹状态信息
 * @param info_buffer 信息缓冲区
 * @param buffer_size 缓冲区大小
 */
void Track_GetStatusInfo(char *info_buffer, uint16_t buffer_size);

/**
 * @brief 获取性能统计
 * @param controller 寻迹控制器指针
 * @param stats_buffer 统计信息缓冲区
 * @param buffer_size 缓冲区大小
 */
void Track_GetPerformanceStats(TrackController_t *controller, char *stats_buffer, uint16_t buffer_size);

/**
 * @brief 重置性能统计
 * @param controller 寻迹控制器指针
 */
void Track_ResetStats(TrackController_t *controller);

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif

#endif /* ENHANCED_GANWAY_H */
