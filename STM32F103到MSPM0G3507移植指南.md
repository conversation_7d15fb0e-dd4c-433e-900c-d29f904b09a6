# STM32F103寻迹小车移植到MSPM0G3507完整指南

## 文档使用说明

本移植指南包含两个核心文档：

1. **STM32F103到MSPM0G3507移植指南.md**（本文档）
   - 完整的技术分析和移植方案
   - 详细的代码示例和配置方法
   - 分步骤的移植流程
   - 适合深入学习和完整实施

2. **移植快速参考卡.md**
   - 关键信息速查表
   - API映射和常见问题
   - 适合移植过程中快速查询

**建议使用方式**：
- 首次移植：完整阅读本指南
- 实际编码：配合快速参考卡使用
- 问题排查：优先查看参考卡的常见问题部分

## 项目概述

本指南详细介绍如何将基于STM32F103C8的寻迹小车项目移植到TI MSPM0G3507微控制器平台，并在Code Composer Studio (CCS)开发环境中进行开发。

## 1. 硬件架构差异分析与解决方案

### 1.1 核心架构对比

| 特性 | STM32F103C8 | MSPM0G3507 | 影响分析 |
|------|-------------|------------|----------|
| 内核 | ARM Cortex-M3 | ARM Cortex-M0+ | 指令集简化，性能略降 |
| 主频 | 72MHz | 80MHz | 性能提升，时钟配置需调整 |
| Flash | 64KB | 128KB | 存储空间翻倍 |
| SRAM | 20KB | 32KB | 内存充足 |
| 指令集 | Thumb-2 | Thumb | 部分指令不支持 |
| 中断优先级 | 16级 | 4级 | 中断配置需简化 |

### 1.2 关键差异影响

**指令集兼容性**：
- Cortex-M0+不支持某些Thumb-2指令
- 编译器会自动处理大部分兼容性问题
- 需要检查内联汇编代码

**中断处理差异**：
- M0+只支持4级中断优先级（0-3）
- M3支持16级优先级（0-15）
- 需要重新分配中断优先级

**调试接口**：
- 都支持SWD调试接口
- M0+调试功能相对简化
- 断点数量可能有限制

### 1.3 解决方案

1. **编译器设置调整**：
   - 目标架构：从Cortex-M3改为Cortex-M0+
   - 优化级别：建议使用-O2优化
   - 浮点单元：M0+不支持硬件FPU

2. **中断优先级重新分配**：
   ```c
   // STM32F103 (16级优先级)
   NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
   NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
   
   // MSPM0G3507 (4级优先级)
   NVIC_SetPriority(IRQn, 1);  // 0-3级优先级
   ```

## 2. 外设配置差异分析与API映射

### 2.1 GPIO配置对比

**STM32F103 GPIO配置**：
```c
// STM32标准外设库
RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);
GPIO_InitTypeDef GPIO_InitStructure;
GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1;
GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
GPIO_Init(GPIOB, &GPIO_InitStructure);
```

**MSPM0G3507 GPIO配置**：
```c
// MSPM0 SDK
DL_GPIO_initPeripheralOutputPin(GPIO_MOTOR_LEFT_DIR1_IOMUX);
DL_GPIO_initPeripheralOutputPin(GPIO_MOTOR_LEFT_DIR2_IOMUX);
DL_GPIO_setPins(GPIO_MOTOR_LEFT_PORT, GPIO_MOTOR_LEFT_DIR1_PIN);
DL_GPIO_clearPins(GPIO_MOTOR_LEFT_PORT, GPIO_MOTOR_LEFT_DIR2_PIN);
```

### 2.2 PWM配置对比

**STM32F103 PWM配置**：
```c
// 定时器3 PWM配置
TIM_TimeBaseInitStructure.TIM_Period = 20000 - 1;    // ARR
TIM_TimeBaseInitStructure.TIM_Prescaler = 72 - 1;    // PSC
TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;
TIM_OC1Init(TIM3, &TIM_OCInitStructure);
```

**MSPM0G3507 PWM配置**：
```c
// TIMG PWM配置
DL_TimerG_setClockConfig(PWM_INST, (DL_TimerG_ClockConfig *) &gPWMClockConfig);
DL_TimerG_initPWMMode(PWM_INST, (DL_TimerG_PWMConfig *) &gPWMConfig);
DL_TimerG_setCaptureCompareValue(PWM_INST, dutyCycle, DL_TIMER_CC_0_INDEX);
```

### 2.3 ADC配置对比

**STM32F103 ADC配置**：
```c
// ADC1配置
ADC_InitStructure.ADC_Mode = ADC_Mode_Independent;
ADC_InitStructure.ADC_ScanConvMode = DISABLE;
ADC_RegularChannelConfig(ADC1, ADC_Channel_2, 1, ADC_SampleTime_55Cycles5);
```

**MSPM0G3507 ADC配置**：
```c
// ADC12配置
DL_ADC12_setClockConfig(ADC12_0_INST, (DL_ADC12_ClockConfig *) &gADC12_0ClockConfig);
DL_ADC12_initSingleSample(ADC12_0_INST, DL_ADC12_REPEAT_MODE_ENABLED, 
                          DL_ADC12_SAMPLING_SOURCE_AUTO, DL_ADC12_TRIG_SRC_SOFTWARE,
                          DL_ADC12_SAMP_CONV_RES_12_BIT, DL_ADC12_SAMP_CONV_DATA_FORMAT_UNSIGNED);
```

## 3. 开发环境迁移指南

### 3.1 从Keil到CCS的迁移步骤

**步骤1：安装CCS开发环境**
1. 下载并安装Code Composer Studio
2. 安装MSPM0 SDK
3. 配置工具链和调试器

**步骤2：创建新项目**
1. 选择MSPM0G3507目标器件
2. 选择Empty Project模板
3. 配置项目名称和路径

**步骤3：项目配置**
```c
// CCS项目配置要点
- 编译器：TI ARM Clang
- 优化级别：-O2
- 目标架构：Cortex-M0+
- 链接脚本：mspm0g3507.cmd
- 调试配置：XDS110 USB Debug Probe
```

### 3.2 编译器差异处理

**Keil ARM编译器 vs TI ARM Clang**：
- 语法兼容性：基本兼容C99标准
- 内联汇编：语法略有差异
- 优化策略：TI编译器优化更激进
- 链接脚本：格式完全不同

## 4. 库函数替换与API适配

### 4.1 详细API映射表

| 功能类别 | STM32标准外设库 | MSPM0 SDK | 参数差异 | 注意事项 |
|----------|----------------|-----------|----------|----------|
| **GPIO操作** |
| GPIO设置 | `GPIO_SetBits(GPIOB, GPIO_Pin_0)` | `DL_GPIO_setPins(GPIO_PORT, GPIO_PIN)` | 端口宏定义不同 | 需重新定义端口和引脚宏 |
| GPIO清除 | `GPIO_ResetBits(GPIOB, GPIO_Pin_0)` | `DL_GPIO_clearPins(GPIO_PORT, GPIO_PIN)` | 端口宏定义不同 | 同上 |
| GPIO读取 | `GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_0)` | `DL_GPIO_readPins(GPIO_PORT, GPIO_PIN)` | 返回值类型相同 | 布尔值返回 |
| GPIO初始化 | `GPIO_Init(GPIOB, &GPIO_InitStructure)` | `DL_GPIO_initPeripheralOutputPin()` | 配置方式完全不同 | 使用SysConfig配置 |
| **PWM控制** |
| PWM设置 | `TIM_SetCompare1(TIM3, compare)` | `DL_TimerG_setCaptureCompareValue(TIMER_INST, compare, CC_INDEX)` | 增加了通道索引参数 | 需指定比较通道 |
| PWM初始化 | `TIM_OC1Init(TIM3, &TIM_OCInitStructure)` | `DL_TimerG_initPWMMode(TIMER_INST, &config)` | 配置结构体不同 | 配置参数重新组织 |
| **ADC操作** |
| ADC读取 | `ADC_GetConversionValue(ADC1)` | `DL_ADC12_getMemResult(ADC_INST, MEM_IDX)` | 增加了内存索引 | 支持多通道结果存储 |
| ADC启动 | `ADC_SoftwareStartConvCmd(ADC1, ENABLE)` | `DL_ADC12_startConversion(ADC_INST)` | 简化了参数 | 更直观的函数名 |
| ADC状态 | `ADC_GetFlagStatus(ADC1, ADC_FLAG_EOC)` | `DL_ADC12_getStatus(ADC_INST, STATUS_FLAG)` | 状态标志定义不同 | 需重新映射状态标志 |
| **定时器操作** |
| 定时器使能 | `TIM_Cmd(TIM2, ENABLE)` | `DL_TimerG_startCounter(TIMER_INST)` | 更明确的函数名 | 功能相同 |
| 中断清除 | `TIM_ClearITPendingBit(TIM2, TIM_IT_Update)` | `DL_TimerG_clearInterruptStatus(TIMER_INST)` | 自动清除所有中断 | 不需要指定具体中断 |
| **时钟控制** |
| 外设时钟使能 | `RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE)` | 自动管理 | MSPM0自动管理外设时钟 | 无需手动使能 |

### 4.2 完整适配层设计

**hal_adapter.h - 适配层头文件**：
```c
#ifndef HAL_ADAPTER_H
#define HAL_ADAPTER_H

#include "ti_msp_dl_config.h"

// 引脚定义适配
#define GPIO_Pin_0    GPIO_MOTOR_LEFT_DIR1_PIN
#define GPIO_Pin_1    GPIO_MOTOR_LEFT_DIR2_PIN
#define GPIO_Pin_4    GPIO_MOTOR_RIGHT_DIR1_PIN
#define GPIO_Pin_5    GPIO_MOTOR_RIGHT_DIR2_PIN

// 端口定义适配
#define GPIOA         GPIO_MOTOR_RIGHT_PORT
#define GPIOB         GPIO_MOTOR_LEFT_PORT

// GPIO操作适配宏
#define GPIO_SetBits(port, pin)    DL_GPIO_setPins(port, pin)
#define GPIO_ResetBits(port, pin)  DL_GPIO_clearPins(port, pin)
#define GPIO_ReadInputDataBit(port, pin) DL_GPIO_readPins(port, pin)

// PWM适配函数声明
void PWM_SetCompare1(uint16_t compare);
void PWM_SetCompare2(uint16_t compare);
void PWM_Init(void);
void PWM2_Init(void);

// ADC适配函数声明
uint16_t ADC_GetValue(void);
void ADC_Config(void);

// 定时器适配函数声明
void Timer_Init(void);

// 延时适配函数
void Delay_ms(uint32_t ms);
void Delay_us(uint32_t us);

#endif
```

**hal_adapter.c - 适配层实现**：
```c
#include "hal_adapter.h"

// PWM适配函数实现
void PWM_SetCompare1(uint16_t compare)
{
    // 右电机PWM控制
    DL_TimerG_setCaptureCompareValue(PWM_RIGHT_INST, compare, DL_TIMER_CC_0_INDEX);
}

void PWM_SetCompare2(uint16_t compare)
{
    // 左电机PWM控制
    DL_TimerG_setCaptureCompareValue(PWM_LEFT_INST, compare, DL_TIMER_CC_0_INDEX);
}

void PWM_Init(void)
{
    // 右电机PWM初始化
    DL_TimerG_setClockConfig(PWM_RIGHT_INST, (DL_TimerG_ClockConfig *) &gPWMRightClockConfig);
    DL_TimerG_initPWMMode(PWM_RIGHT_INST, (DL_TimerG_PWMConfig *) &gPWMRightConfig);
    DL_TimerG_enableClock(PWM_RIGHT_INST);
    DL_TimerG_startCounter(PWM_RIGHT_INST);
}

void PWM2_Init(void)
{
    // 左电机PWM初始化
    DL_TimerG_setClockConfig(PWM_LEFT_INST, (DL_TimerG_ClockConfig *) &gPWMLeftClockConfig);
    DL_TimerG_initPWMMode(PWM_LEFT_INST, (DL_TimerG_PWMConfig *) &gPWMLeftConfig);
    DL_TimerG_enableClock(PWM_LEFT_INST);
    DL_TimerG_startCounter(PWM_LEFT_INST);
}

// ADC适配函数实现
uint16_t ADC_GetValue(void)
{
    // 启动ADC转换
    DL_ADC12_startConversion(ADC12_0_INST);

    // 等待转换完成
    while (DL_ADC12_isBusy(ADC12_0_INST));

    // 读取转换结果
    return DL_ADC12_getMemResult(ADC12_0_INST, DL_ADC12_MEM_IDX_0);
}

void ADC_Config(void)
{
    // ADC配置已在SysConfig中完成，这里只需要使能
    DL_ADC12_enableConversions(ADC12_0_INST);
}

// 定时器适配函数实现
void Timer_Init(void)
{
    // 1秒定时器初始化
    DL_TimerG_setClockConfig(TIMER_1S_INST, (DL_TimerG_ClockConfig *) &gTimer1SClockConfig);
    DL_TimerG_initTimerMode(TIMER_1S_INST, (DL_TimerG_TimerConfig *) &gTimer1SConfig);
    DL_TimerG_enableInterrupt(TIMER_1S_INST, DL_TIMER_INTERRUPT_ZERO_EVENT);

    NVIC_SetPriority(TIMER_1S_INST_INT_IRQN, 1);
    NVIC_EnableIRQ(TIMER_1S_INST_INT_IRQN);

    DL_TimerG_enableClock(TIMER_1S_INST);
    DL_TimerG_startCounter(TIMER_1S_INST);
}

// 延时适配函数实现
void Delay_ms(uint32_t ms)
{
    // 基于80MHz系统时钟的延时
    DL_Common_delayCycles(ms * 80000);
}

void Delay_us(uint32_t us)
{
    // 微秒级延时
    DL_Common_delayCycles(us * 80);
}
```

### 4.3 SysConfig配置文件

MSPM0G3507使用SysConfig图形化配置工具，需要创建对应的配置：

**ti_msp_dl_config.h - 自动生成的配置头文件**：
```c
// 这个文件由SysConfig自动生成，包含所有外设配置

// GPIO配置定义
#define GPIO_MOTOR_LEFT_PORT              (GPIOA)
#define GPIO_MOTOR_LEFT_DIR1_PIN          (DL_GPIO_PIN_0)
#define GPIO_MOTOR_LEFT_DIR1_IOMUX        (IOMUX_PINCM1)
#define GPIO_MOTOR_LEFT_DIR2_PIN          (DL_GPIO_PIN_1)
#define GPIO_MOTOR_LEFT_DIR2_IOMUX        (IOMUX_PINCM2)

#define GPIO_MOTOR_RIGHT_PORT             (GPIOA)
#define GPIO_MOTOR_RIGHT_DIR1_PIN         (DL_GPIO_PIN_3)
#define GPIO_MOTOR_RIGHT_DIR1_IOMUX       (IOMUX_PINCM4)
#define GPIO_MOTOR_RIGHT_DIR2_PIN         (DL_GPIO_PIN_4)
#define GPIO_MOTOR_RIGHT_DIR2_IOMUX       (IOMUX_PINCM5)

// PWM配置定义
#define PWM_LEFT_INST                     (TIMG0)
#define PWM_LEFT_INST_IRQHandler          TIMG0_IRQHandler
#define PWM_LEFT_INST_INT_IRQN            (TIMG0_INT_IRQn)

#define PWM_RIGHT_INST                    (TIMG1)
#define PWM_RIGHT_INST_IRQHandler         TIMG1_IRQHandler
#define PWM_RIGHT_INST_INT_IRQN           (TIMG1_INT_IRQn)

// ADC配置定义
#define ADC12_0_INST                      (ADC0)
#define ADC12_0_INST_IRQHandler           ADC0_IRQHandler
#define ADC12_0_INST_INT_IRQN             (ADC0_INT_IRQn)

// 定时器配置定义
#define TIMER_1S_INST                     (TIMG2)
#define TIMER_1S_INST_IRQHandler          TIMG2_IRQHandler
#define TIMER_1S_INST_INT_IRQN            (TIMG2_INT_IRQn)
```

### 4.4 移植后的主要模块修改

**Motor.c 模块适配**：
```c
// 原STM32代码保持不变，通过适配层实现兼容
#include "hal_adapter.h"  // 替换原来的stm32f10x.h

// 函数实现保持完全不变
void Left_moto_go(void)
{
    GPIO_SetBits(GPIOB, GPIO_Pin_0);   // 通过适配层调用
    GPIO_ResetBits(GPIOB, GPIO_Pin_1);
}

void Right_moto_go(void)
{
    GPIO_SetBits(GPIOA, GPIO_Pin_4);   // 通过适配层调用
    GPIO_ResetBits(GPIOA, GPIO_Pin_5);
}
```

**MotorRun.c 模块适配**：
```c
#include "hal_adapter.h"  // 替换头文件

// 函数实现保持不变
void dual_run(uint16_t LeftSpeed, uint16_t RightSpeed)
{
    PWM_SetCompare2(LeftSpeed * 200);  // 通过适配层调用
    PWM_SetCompare1(RightSpeed * 200);
    Left_moto_go();
    Right_moto_go();
}
```

这样的适配层设计确保了：
1. **上层应用代码无需修改**：寻迹算法和控制逻辑保持不变
2. **底层硬件透明**：硬件差异被适配层屏蔽
3. **维护性好**：只需要维护适配层，上层代码保持稳定
4. **可扩展性强**：可以轻松添加新的外设适配

## 5. 引脚映射调整与硬件连接重设计

### 5.1 原STM32F103引脚使用

| 功能 | STM32F103引脚 | 说明 |
|------|---------------|------|
| 左电机方向 | PB0, PB1 | GPIO输出 |
| 左电机PWM | PB6 | TIM4_CH1 |
| 右电机方向 | PA4, PA5 | GPIO输出 |
| 右电机PWM | PA6 | TIM3_CH1 |
| 右编码器 | PA8, PA9 | TIM1_CH1, TIM1_CH2 |
| 左编码器 | PA0, PA1 | 外部中断 |
| 传感器ADC | PA2 | ADC1_IN2 |
| 传感器地址线 | PB12, PB13, PB14 | GPIO输出 |
| OLED I2C | PB8, PB9 | I2C1_SCL, I2C1_SDA |

### 5.2 MSPM0G3507引脚重新分配

| 功能 | MSPM0G3507引脚 | 复用功能 | 说明 |
|------|----------------|----------|------|
| 左电机方向 | PA0, PA1 | GPIO | 通用GPIO |
| 左电机PWM | PA2 | TIMG0_CCP0 | PWM输出 |
| 右电机方向 | PA3, PA4 | GPIO | 通用GPIO |
| 右电机PWM | PA5 | TIMG0_CCP1 | PWM输出 |
| 右编码器 | PA6, PA7 | TIMG1_CCP0, TIMG1_CCP1 | 编码器模式 |
| 左编码器 | PA8, PA9 | GPIO | 外部中断 |
| 传感器ADC | PA10 | ADC0_CH5 | ADC输入 |
| 传感器地址线 | PA11, PA12, PA13 | GPIO | 地址控制 |
| OLED I2C | PA14, PA15 | I2C0_SCL, I2C0_SDA | 硬件I2C |

### 5.3 硬件连接图

```
MSPM0G3507引脚分配：
PA0  ──── 左电机方向1
PA1  ──── 左电机方向2  
PA2  ──── 左电机PWM (TIMG0_CCP0)
PA3  ──── 右电机方向1
PA4  ──── 右电机方向2
PA5  ──── 右电机PWM (TIMG0_CCP1)
PA6  ──── 右编码器A相 (TIMG1_CCP0)
PA7  ──── 右编码器B相 (TIMG1_CCP1)
PA8  ──── 左编码器A相 (外部中断)
PA9  ──── 左编码器B相 (GPIO)
PA10 ──── 传感器ADC输入 (ADC0_CH5)
PA11 ──── 传感器地址线0
PA12 ──── 传感器地址线1
PA13 ──── 传感器地址线2
PA14 ──── OLED_SCL (I2C0_SCL)
PA15 ──── OLED_SDA (I2C0_SDA)
```

## 6. 时钟配置修改与优化

### 6.1 时钟系统对比

**STM32F103时钟配置**：
- 外部晶振：8MHz
- 系统时钟：72MHz (通过PLL倍频)
- APB1时钟：36MHz
- APB2时钟：72MHz
- 定时器时钟：72MHz

**MSPM0G3507时钟配置**：
- 内部振荡器：32MHz SYSOSC
- 系统时钟：80MHz (通过PLL倍频)
- 外设时钟：80MHz
- 定时器时钟：80MHz

### 6.2 时钟配置代码

```c
// MSPM0G3507时钟配置
static const DL_SYSCTL_SYSOSCConfig gSYSOSCConfig = {
    .freq = DL_SYSCTL_SYSOSC_FREQ_BASE,
    .monitor = false,
};

static const DL_SYSCTL_CLKConfig gCLKConfig = {
    .sysosc = &gSYSOSCConfig,
    .sysPLLConfig = {
        .enableSysPLL = true,
        .sysPLLSource = DL_SYSCTL_SYSPLL_SOURCE_SYSOSC,
        .sysPLLRef = DL_SYSCTL_SYSPLL_REF_CLK_IN_32MHZ,
        .qDiv = 1,
        .pDiv = DL_SYSCTL_SYSPLL_PDIV_2,
    },
    .ulpClkDiv = 1,
    .bclkDiv = 1,
    .mclkDiv = 1,
    .cpuClkDiv = 1,
};
```

### 6.3 PWM频率调整

由于时钟频率从72MHz提升到80MHz，需要调整PWM参数：

```c
// STM32F103: 50Hz PWM (20ms周期)
// 预分频器: 72-1, 自动重装载: 20000-1
// 实际频率 = 72MHz / 72 / 20000 = 50Hz

// MSPM0G3507: 50Hz PWM (20ms周期)  
// 需要调整分频参数以保持相同频率
// 实际频率 = 80MHz / 80 / 20000 = 50Hz
```

## 7. 中断处理变化适配

### 7.1 中断优先级重新分配

**STM32F103中断配置**：
```c
// 16级优先级配置
NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
NVIC_Init(&NVIC_InitStructure);
```

**MSPM0G3507中断配置**：
```c
// 4级优先级配置
NVIC_SetPriority(TIMER_0_INST_INT_IRQN, 1);
NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);
```

### 7.2 中断服务程序适配

**定时器中断适配**：
```c
// STM32F103定时器中断
void TIM2_IRQHandler(void)
{
    if (TIM_GetITStatus(TIM2, TIM_IT_Update) == SET)
    {
        // 中断处理代码
        TIM_ClearITPendingBit(TIM2, TIM_IT_Update);
    }
}

// MSPM0G3507定时器中断
void TIMER_0_INST_IRQHandler(void)
{
    switch (DL_TimerG_getPendingInterrupt(TIMER_0_INST)) {
        case DL_TIMER_IIDX_ZERO:
            // 中断处理代码
            break;
        default:
            break;
    }
}
```

**外部中断适配**：
```c
// STM32F103外部中断
void EXTI0_IRQHandler(void)
{
    if(EXTI_GetITStatus(EXTI_Line0) != RESET)
    {
        // 编码器中断处理
        EXTI_ClearITPendingBit(EXTI_Line0);
    }
}

// MSPM0G3507外部中断
void GROUP1_IRQHandler(void)
{
    switch (DL_Interrupt_getPendingGroup(DL_INTERRUPT_GROUP_1)) {
        case GPIO_ENCODER_INT_IIDX:
            // 编码器中断处理
            DL_GPIO_clearInterruptStatus(GPIO_ENCODER_PORT, GPIO_ENCODER_PIN);
            break;
        default:
            break;
    }
}
```

## 8. 移植流程指南与验证测试

### 8.1 分步骤移植流程

**阶段1：环境准备**
- [ ] 安装CCS开发环境和MSPM0 SDK
- [ ] 创建新的MSPM0G3507项目
- [ ] 配置基本的时钟和GPIO
- [ ] 验证基础编译和下载功能

**阶段2：基础外设移植**
- [ ] 移植GPIO控制（电机方向控制）
- [ ] 移植PWM输出（电机调速）
- [ ] 创建适配层函数
- [ ] 验证电机基本控制功能
- [ ] 测试PWM频率和占空比

**阶段3：传感器系统移植**
- [ ] 移植ADC配置和读取
- [ ] 移植灰度传感器地址线控制
- [ ] 验证8路传感器独立读取
- [ ] 测试ADC精度和稳定性
- [ ] 验证传感器数据二值化

**阶段4：显示和通信移植**
- [ ] 移植I2C配置（OLED显示）
- [ ] 移植OLED显示驱动
- [ ] 验证字符和数字显示
- [ ] 测试I2C通信稳定性
- [ ] 验证实时数据显示

**阶段5：编码器和中断移植**
- [ ] 移植编码器接口配置
- [ ] 移植定时器和外部中断
- [ ] 验证中断响应时间
- [ ] 测试编码器计数精度
- [ ] 验证速度反馈功能

**阶段6：系统集成和测试**
- [ ] 集成所有模块
- [ ] 移植寻迹算法
- [ ] 整体功能测试
- [ ] 性能对比测试
- [ ] 长时间稳定性测试

### 8.2 验证测试方案

**单元测试**：
- GPIO输出测试：LED闪烁测试
- PWM输出测试：示波器测量PWM波形
- ADC输入测试：电压测量精度验证
- I2C通信测试：OLED显示字符测试
- 中断响应测试：中断频率和延迟测量

**集成测试**：
- 电机控制测试：前进、后退、转向功能
- 传感器测试：8路传感器数据采集
- 寻迹算法测试：直线和弯道寻迹性能
- 系统稳定性测试：长时间运行测试

**性能对比测试**：
- 寻迹精度对比
- 响应速度对比  
- 功耗对比
- 稳定性对比

### 8.3 移植检查清单

**硬件检查**：
- [ ] 引脚连接正确
- [ ] 电源电压匹配
- [ ] 时钟配置正确
- [ ] 外设功能正常

**软件检查**：
- [ ] 编译无错误无警告
- [ ] 所有API调用正确
- [ ] 中断配置正确
- [ ] 内存使用合理

**功能检查**：
- [ ] 电机控制正常
- [ ] 传感器读取正常
- [ ] 显示功能正常
- [ ] 寻迹算法正常

### 8.4 常见问题和详细解决方案

**问题1：编译错误 - 未定义的函数或宏**
```
错误信息：undefined reference to 'GPIO_SetBits'
```
- **原因**：STM32标准外设库函数在MSPM0 SDK中不存在
- **解决方案**：
  1. 使用适配层：`#include "hal_adapter.h"`
  2. 或直接替换：`GPIO_SetBits(GPIOB, GPIO_Pin_0)` → `DL_GPIO_setPins(GPIO_PORT, GPIO_PIN)`
- **验证方法**：编译通过且功能正常

**问题2：PWM频率不正确**
```
期望：50Hz PWM输出
实际：55.6Hz PWM输出
```
- **原因**：时钟频率从72MHz变为80MHz，但分频参数未调整
- **详细分析**：
  ```c
  // STM32F103计算：72MHz / 72 / 20000 = 50Hz
  // MSPM0G3507错误计算：80MHz / 72 / 20000 = 55.6Hz
  // MSPM0G3507正确计算：80MHz / 80 / 20000 = 50Hz
  ```
- **解决方案**：
  ```c
  // 在SysConfig中配置PWM
  // Clock Divider: 80 (替代原来的72)
  // Period: 20000
  // 或者在代码中动态调整
  DL_TimerG_setLoadValue(PWM_INST, 20000-1);
  DL_TimerG_setClockDivider(PWM_INST, 80);
  ```
- **验证方法**：使用示波器测量PWM频率

**问题3：中断不响应**
```
现象：定时器配置正确但中断函数不执行
```
- **原因分析**：
  1. 中断向量表配置错误
  2. 中断优先级超出M0+支持范围（0-3）
  3. 中断使能顺序错误
- **解决方案**：
  ```c
  // 正确的中断配置顺序
  // 1. 配置中断优先级（0-3）
  NVIC_SetPriority(TIMER_1S_INST_INT_IRQN, 1);

  // 2. 使能NVIC中断
  NVIC_EnableIRQ(TIMER_1S_INST_INT_IRQN);

  // 3. 使能定时器中断
  DL_TimerG_enableInterrupt(TIMER_1S_INST, DL_TIMER_INTERRUPT_ZERO_EVENT);

  // 4. 启动定时器
  DL_TimerG_startCounter(TIMER_1S_INST);
  ```
- **调试方法**：
  1. 在中断函数中添加GPIO翻转验证
  2. 使用CCS调试器设置断点
  3. 检查中断状态寄存器

**问题4：ADC读取值异常**
```
现象：ADC读取值始终为0或4095
```
- **原因分析**：
  1. 参考电压配置错误（VREF vs VCC）
  2. ADC通道映射错误
  3. 采样时间不足
  4. 输入阻抗过高
- **解决方案**：
  ```c
  // 检查ADC配置
  DL_ADC12_configConversionMem(ADC12_0_INST, DL_ADC12_MEM_IDX_0,
      DL_ADC12_INPUT_CHAN_5,           // 确认通道号正确
      DL_ADC12_REFERENCE_VOLTAGE_VDDA, // 使用VDD作为参考
      DL_ADC12_SAMPLE_TIMER_SOURCE_SCOMP0,
      DL_ADC12_AVERAGING_MODE_DISABLED,
      DL_ADC12_BURN_OUT_SOURCE_DISABLED,
      DL_ADC12_TRIGGER_MODE_AUTO_NEXT,
      DL_ADC12_WINDOWS_COMP_MODE_DISABLED);

  // 增加采样时间
  DL_ADC12_setSampleTime0(ADC12_0_INST, 1000); // 增加采样时间
  ```
- **验证方法**：
  1. 使用万用表测量输入电压
  2. 逐步调试：先测试固定电压，再测试传感器

**问题5：I2C通信失败**
```
现象：OLED显示空白或花屏
```
- **原因分析**：
  1. I2C时序配置错误
  2. 上拉电阻缺失或阻值不当
  3. 从设备地址错误
  4. 时钟频率过高
- **解决方案**：
  ```c
  // 配置I2C为标准模式100kHz
  static const DL_I2C_ClockConfig gI2CClockConfig = {
      .clockSel = DL_I2C_CLOCK_BUSCLK,
      .divideRatio = DL_I2C_CLOCK_DIVIDE_RATIO_1,
  };

  static const DL_I2C_ControllerConfig gI2CControllerConfig = {
      .analogGlitchFilter = DL_I2C_ANALOG_GLITCH_FILTER_DISABLED,
      .digitalGlitchFilter = DL_I2C_DIGITAL_GLITCH_FILTER_DISABLED,
      .clockStretch = DL_I2C_CONTROLLER_CLOCK_STRETCH_DISABLED,
      .isClockStretchingEnabled = false,
      .targetAddress = 0x3C,  // OLED地址
  };

  // 设置I2C速度为100kHz
  DL_I2C_setClockConfig(I2C_INST, (DL_I2C_ClockConfig *) &gI2CClockConfig);
  DL_I2C_setTimerPeriod(I2C_INST, 400);  // 100kHz @ 80MHz
  ```
- **调试方法**：
  1. 使用示波器检查SCL/SDA波形
  2. 确认上拉电阻（通常4.7kΩ）
  3. 逐步降低I2C频率测试

**问题6：编码器计数异常**
```
现象：编码器计数跳变或方向错误
```
- **原因分析**：
  1. 编码器信号抖动
  2. 中断触发边沿配置错误
  3. 中断处理时间过长
- **解决方案**：
  ```c
  // 增加软件防抖
  void Encoder_IRQHandler(void)
  {
      static uint32_t last_time = 0;
      uint32_t current_time = DL_Common_getCycleCount();

      // 防抖：间隔小于1ms的中断忽略
      if (current_time - last_time < 80000) {  // 80MHz时钟，1ms = 80000周期
          return;
      }
      last_time = current_time;

      // 编码器处理逻辑
      uint8_t current_A = DL_GPIO_readPins(ENCODER_PORT, ENCODER_A_PIN);
      uint8_t current_B = DL_GPIO_readPins(ENCODER_PORT, ENCODER_B_PIN);

      // 四倍频编码器解码
      if (current_A != last_A) {
          if (current_A == current_B) {
              encoder_count++;
          } else {
              encoder_count--;
          }
      }

      last_A = current_A;
      last_B = current_B;
  }
  ```

**问题7：系统复位或死机**
```
现象：系统运行一段时间后自动复位
```
- **原因分析**：
  1. 看门狗超时
  2. 栈溢出
  3. 中断嵌套过深
  4. 内存访问越界
- **解决方案**：
  ```c
  // 1. 检查栈大小配置
  // 在链接脚本中增加栈大小
  STACK_SIZE = 0x1000;  // 4KB栈空间

  // 2. 禁用看门狗（调试阶段）
  DL_WWDT_disable(WWDT0);

  // 3. 检查中断优先级
  // 确保所有中断优先级在0-3范围内
  NVIC_SetPriority(IRQn, 2);  // 不要使用超过3的优先级

  // 4. 添加栈溢出检测
  void check_stack_overflow(void)
  {
      extern uint32_t __stack_start__;
      uint32_t *stack_ptr = (uint32_t*)&__stack_start__;
      if (*stack_ptr != 0xDEADBEEF) {
          // 栈溢出检测
          while(1);  // 停止执行
      }
  }
  ```

**问题8：功耗过高**
```
现象：电池续航时间明显缩短
```
- **原因分析**：
  1. 未使用MSPM0的低功耗特性
  2. 外设时钟未优化
  3. GPIO配置不当导致漏电流
- **解决方案**：
  ```c
  // 1. 使用低功耗模式
  DL_SYSCTL_setPowerPolicyRUN0SLEEP0();

  // 2. 动态调整时钟频率
  // 寻迹时使用80MHz，待机时降至32MHz
  void set_low_power_mode(void)
  {
      DL_SYSCTL_setSYSOSCFreq(DL_SYSCTL_SYSOSC_FREQ_BASE);  // 32MHz
  }

  void set_high_performance_mode(void)
  {
      DL_SYSCTL_setSYSOSCFreq(DL_SYSCTL_SYSOSC_FREQ_RANGE_32_TO_48_MHZ);  // 48MHz
  }

  // 3. 优化GPIO配置
  // 未使用的GPIO配置为输入上拉
  DL_GPIO_initPeripheralInputPin(UNUSED_PIN_IOMUX);
  DL_GPIO_setPins(UNUSED_PORT, UNUSED_PIN);  // 上拉
  ```

### 8.5 性能验证和对比测试

**测试项目1：PWM精度测试**
```c
// 测试代码
void test_pwm_accuracy(void)
{
    uint16_t test_values[] = {500, 1000, 1500, 2000};

    for (int i = 0; i < 4; i++) {
        PWM_SetCompare1(test_values[i]);
        Delay_ms(1000);
        // 使用示波器测量实际占空比
        printf("设置值: %d, 期望占空比: %.1f%%\n",
               test_values[i], (float)test_values[i]/20000*100);
    }
}
```

**测试项目2：ADC精度测试**
```c
// ADC精度测试
void test_adc_accuracy(void)
{
    uint32_t sum = 0;
    uint16_t samples[100];

    // 采集100个样本
    for (int i = 0; i < 100; i++) {
        samples[i] = ADC_GetValue();
        sum += samples[i];
        Delay_ms(10);
    }

    // 计算平均值和标准差
    float average = (float)sum / 100;
    float variance = 0;

    for (int i = 0; i < 100; i++) {
        variance += (samples[i] - average) * (samples[i] - average);
    }
    variance /= 100;

    printf("ADC平均值: %.2f, 标准差: %.2f\n", average, sqrt(variance));
}
```

**测试项目3：中断响应时间测试**
```c
// 中断响应时间测试
volatile uint32_t interrupt_start_time = 0;
volatile uint32_t interrupt_response_time = 0;

void test_interrupt_response(void)
{
    // 在中断触发前记录时间
    interrupt_start_time = DL_Common_getCycleCount();

    // 触发中断（例如GPIO中断）
    DL_GPIO_setPins(TEST_PORT, TEST_PIN);
}

void TEST_IRQHandler(void)
{
    // 计算响应时间
    interrupt_response_time = DL_Common_getCycleCount() - interrupt_start_time;

    // 清除中断
    DL_GPIO_clearInterruptStatus(TEST_PORT, TEST_PIN);

    // 响应时间 = interrupt_response_time / 80 (微秒，基于80MHz时钟)
}
```

## 9. 性能优化建议

### 9.1 利用MSPM0G3507优势

1. **更高主频**：80MHz vs 72MHz，提升8.3%性能
2. **更大存储**：128KB Flash vs 64KB，支持更复杂算法
3. **更多RAM**：32KB vs 20KB，支持更多数据缓存
4. **硬件I2C**：替代软件I2C，提升通信效率
5. **更精确ADC**：利用更好的ADC性能提升传感器精度

### 9.2 代码优化建议

1. **算法优化**：利用更大内存实现更复杂的寻迹算法
2. **中断优化**：合理分配4级中断优先级
3. **功耗优化**：利用MSPM0的低功耗特性
4. **调试优化**：利用CCS的高级调试功能

## 10. 完整移植示例：灰度传感器模块

为了更好地说明移植过程，这里提供一个完整的模块移植示例。

### 10.1 原STM32F103灰度传感器模块

**原始Grayscale.c文件关键部分**：
```c
// STM32F103版本
#include "stm32f10x.h"
#include "Grayscale.h"
#include "ADC.h"

// 地址线控制宏定义
#define Switch_Address_0(i) ((i)?(GPIO_SetBits(GPIOB, GPIO_Pin_12)) : (GPIO_ResetBits(GPIOB, GPIO_Pin_12)))
#define Switch_Address_1(i) ((i)?(GPIO_SetBits(GPIOB, GPIO_Pin_13)) : (GPIO_ResetBits(GPIOB, GPIO_Pin_13)))
#define Switch_Address_2(i) ((i)?(GPIO_SetBits(GPIOB, GPIO_Pin_14)) : (GPIO_ResetBits(GPIOB, GPIO_Pin_14)))

void Grayscale_Init(void)
{
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);

    GPIO_InitTypeDef GPIO_InitStructure;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_12 | GPIO_Pin_13 | GPIO_Pin_14;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOB, &GPIO_InitStructure);

    GPIO_ResetBits(GPIOB, GPIO_Pin_12 | GPIO_Pin_13 | GPIO_Pin_14);
}

void Get_Analog_value(uint16_t *result)
{
    uint8_t i,j;
    uint32_t Anolag=0;

    for(i=0;i<8;i++)
    {
        Switch_Address_0(!(i&0x01));
        Switch_Address_1(!(i&0x02));
        Switch_Address_2(!(i&0x04));

        for(j=0;j<8;j++)
        {
            Anolag+=ADC_GetValue();
        }
        result[i]=Anolag/8;
        Anolag=0;
    }
}
```

### 10.2 移植后的MSPM0G3507版本

**第一步：创建SysConfig配置**
在CCS中使用SysConfig配置工具：
1. 添加GPIO模块，配置PA11/PA12/PA13为输出
2. 添加ADC模块，配置PA10为ADC输入
3. 生成配置代码

**第二步：修改后的Grayscale.c**：
```c
// MSPM0G3507版本
#include "ti_msp_dl_config.h"
#include "Grayscale.h"

// 地址线控制宏定义 - 适配MSPM0
#define Switch_Address_0(i) ((i)?(DL_GPIO_setPins(GPIO_SENSOR_PORT, GPIO_SENSOR_ADDR0_PIN)) : (DL_GPIO_clearPins(GPIO_SENSOR_PORT, GPIO_SENSOR_ADDR0_PIN)))
#define Switch_Address_1(i) ((i)?(DL_GPIO_setPins(GPIO_SENSOR_PORT, GPIO_SENSOR_ADDR1_PIN)) : (DL_GPIO_clearPins(GPIO_SENSOR_PORT, GPIO_SENSOR_ADDR1_PIN)))
#define Switch_Address_2(i) ((i)?(DL_GPIO_setPins(GPIO_SENSOR_PORT, GPIO_SENSOR_ADDR2_PIN)) : (DL_GPIO_clearPins(GPIO_SENSOR_PORT, GPIO_SENSOR_ADDR2_PIN)))

void Grayscale_Init(void)
{
    // MSPM0的GPIO初始化已在SysConfig中完成
    // 这里只需要设置初始状态
    DL_GPIO_clearPins(GPIO_SENSOR_PORT,
                      GPIO_SENSOR_ADDR0_PIN |
                      GPIO_SENSOR_ADDR1_PIN |
                      GPIO_SENSOR_ADDR2_PIN);

    // 初始化ADC
    DL_ADC12_enableConversions(ADC12_0_INST);
}

void Get_Analog_value(uint16_t *result)
{
    uint8_t i,j;
    uint32_t Anolag=0;

    for(i=0;i<8;i++)
    {
        // 地址线控制逻辑保持不变
        Switch_Address_0(!(i&0x01));
        Switch_Address_1(!(i&0x02));
        Switch_Address_2(!(i&0x04));

        // 稍微延时确保地址线稳定
        DL_Common_delayCycles(800);  // 10us @ 80MHz

        // ADC采样逻辑适配
        for(j=0;j<8;j++)
        {
            // 启动ADC转换
            DL_ADC12_startConversion(ADC12_0_INST);

            // 等待转换完成
            while (DL_ADC12_isBusy(ADC12_0_INST));

            // 读取结果
            Anolag += DL_ADC12_getMemResult(ADC12_0_INST, DL_ADC12_MEM_IDX_0);
        }
        result[i]=Anolag/8;
        Anolag=0;
    }
}

// 其他函数保持相同的逻辑，只需要替换底层API调用
void convertAnalogToDigital(uint16_t *adc_value, uint16_t *Gray_white, uint16_t *Gray_black, uint8_t *Digital)
{
    // 算法逻辑完全不变，只是数据类型保持兼容
    *Digital = 0;
    for (int i = 0; i < 8; i++) {
        if (adc_value[i] < Gray_black[i]) {
            *Digital |= (1 << i);
        } else if (adc_value[i] > Gray_white[i]) {
            *Digital &= ~(1 << i);
        } else {
            uint16_t mid_threshold = (Gray_white[i] + Gray_black[i]) / 2;
            if (adc_value[i] < mid_threshold) {
                *Digital |= (1 << i);
            } else {
                *Digital &= ~(1 << i);
            }
        }
    }
}
```

**第三步：对应的SysConfig配置文件内容**：
```c
// ti_msp_dl_config.h中的相关定义（自动生成）
#define GPIO_SENSOR_PORT                    (GPIOA)
#define GPIO_SENSOR_ADDR0_PIN               (DL_GPIO_PIN_11)
#define GPIO_SENSOR_ADDR0_IOMUX             (IOMUX_PINCM12)
#define GPIO_SENSOR_ADDR1_PIN               (DL_GPIO_PIN_12)
#define GPIO_SENSOR_ADDR1_IOMUX             (IOMUX_PINCM13)
#define GPIO_SENSOR_ADDR2_PIN               (DL_GPIO_PIN_13)
#define GPIO_SENSOR_ADDR2_IOMUX             (IOMUX_PINCM14)

#define ADC12_0_INST                        (ADC0)
#define ADC12_0_INST_IRQHandler             ADC0_IRQHandler
#define ADC12_0_INST_INT_IRQN               (ADC0_INT_IRQn)
#define GPIO_ADC_PIN_10_IOMUX               (IOMUX_PINCM11)
```

### 10.3 移植对比总结

| 移植方面 | 修改程度 | 说明 |
|----------|----------|------|
| **算法逻辑** | 无需修改 | 二值化算法、滤波算法完全保持不变 |
| **数据结构** | 无需修改 | Grayscale_Sensor结构体保持不变 |
| **函数接口** | 无需修改 | 对外接口函数签名保持不变 |
| **底层API** | 完全替换 | GPIO和ADC API调用全部替换 |
| **硬件配置** | 重新配置 | 使用SysConfig重新配置外设 |
| **引脚分配** | 重新分配 | 根据MSPM0引脚能力重新分配 |

### 10.4 移植验证测试

**功能验证代码**：
```c
// 测试传感器模块移植是否成功
void test_grayscale_sensor(void)
{
    Grayscale_Sensor sensor;

    // 初始化传感器
    Grayscale_Init();
    Grayscale_Sensor_Init_First(&sensor);

    printf("开始传感器测试...\n");

    for (int test_cycle = 0; test_cycle < 10; test_cycle++) {
        // 读取传感器数据
        Grayscale_ReadAll(&sensor);

        // 显示原始ADC值
        printf("ADC值: ");
        for (int i = 0; i < 8; i++) {
            printf("%4d ", sensor.Analog_value[i]);
        }

        // 显示数字化结果
        uint8_t digital = Grayscale_GetDigital(&sensor);
        printf("| 数字值: ");
        for (int i = 7; i >= 0; i--) {
            printf("%d", (digital >> i) & 1);
        }
        printf("\n");

        Delay_ms(500);
    }

    printf("传感器测试完成\n");
}
```

**性能对比测试**：
```c
// 对比STM32和MSPM0的传感器读取速度
void benchmark_sensor_speed(void)
{
    uint32_t start_time, end_time;
    uint16_t test_data[8];

    // 测试1000次传感器读取的时间
    start_time = DL_Common_getCycleCount();

    for (int i = 0; i < 1000; i++) {
        Get_Analog_value(test_data);
    }

    end_time = DL_Common_getCycleCount();

    // 计算平均每次读取时间（微秒）
    uint32_t total_cycles = end_time - start_time;
    float avg_time_us = (float)total_cycles / 1000 / 80;  // 80MHz时钟

    printf("传感器读取性能:\n");
    printf("1000次读取总时间: %.2f ms\n", (float)total_cycles / 80000);
    printf("平均每次读取时间: %.2f us\n", avg_time_us);
    printf("读取频率: %.1f Hz\n", 1000000.0 / avg_time_us);
}
```

## 11. 总结与最佳实践

### 11.1 移植成功的关键因素

1. **充分理解原系统**：深入分析STM32代码的硬件依赖和算法逻辑
2. **合理的适配层设计**：最小化上层代码修改，集中处理底层差异
3. **分阶段验证**：每个模块移植后立即验证，避免问题累积
4. **充分的测试**：功能测试、性能测试、稳定性测试缺一不可

### 11.2 移植后的优势

1. **性能提升**：80MHz vs 72MHz，提升11%的处理能力
2. **存储翻倍**：128KB Flash vs 64KB，支持更复杂的算法
3. **内存充足**：32KB RAM vs 20KB，支持更多数据缓存
4. **开发体验**：CCS提供更好的调试和分析工具
5. **成本优势**：MSPM0系列通常具有更好的性价比

### 11.3 后续优化建议

1. **算法优化**：利用更大内存实现更精确的寻迹算法
2. **功耗优化**：利用MSPM0的低功耗特性延长续航
3. **通信扩展**：利用CAN-FD接口实现车车通信
4. **传感器融合**：增加IMU传感器提升寻迹稳定性

通过本移植指南，可以成功将STM32F103寻迹小车项目移植到MSPM0G3507平台。移植后的系统将具有更好的性能、更大的存储空间和更丰富的外设功能。关键是要理解两个平台的差异，合理设计适配层，并进行充分的测试验证。

移植完成后，建议进行性能对比测试，验证移植的成功性，并根据新平台的特性进行进一步的优化。
