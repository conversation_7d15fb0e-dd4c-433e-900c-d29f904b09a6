#include "enhanced_ganway.h"

/**
 * @file enhanced_ganway.c
 * @brief 增强寻迹算法实现文件 - 基于状态机设计模式
 * @details 重构简单if-else逻辑为智能状态机，参考key.c的状态机模式
 * <AUTHOR>
 */

/**************************** 全局变量定义 ****************************/

// 寻迹控制器实例
TrackController_t track_controller;

// 状态名称字符串（用于调试）
static const char* state_names[] = {
    "IDLE", "STRAIGHT", "LEFT_SLIGHT", "RIGHT_SLIGHT",
    "LEFT_TURN", "RIGHT_TURN", "LEFT_SHARP", "RIGHT_SHARP",
    "LOST_LINE", "STOP", "REVERSE", "SEARCH"
};

/**************************** 核心寻迹函数实现 ****************************/

/**
 * @brief 增强寻迹主函数 - 保持与原Way函数接口兼容
 * @param sensor_data 8位传感器数据
 * @note 替换原Ganway.c中的Way函数，内部使用状态机实现
 */
void Way(unsigned char sensor_data)
{
    // 调用状态机处理函数
    Track_StateMachine_Process(&track_controller, sensor_data);
}

/**
 * @brief 寻迹控制器初始化
 * @param controller 寻迹控制器指针
 */
void Track_Controller_Init(TrackController_t *controller)
{
    // 初始化状态机
    controller->current_state = TRACK_STATE_IDLE;
    controller->last_state = TRACK_STATE_IDLE;
    controller->last_event = TRACK_EVENT_NONE;
    controller->state_enter_time = get_systicks();
    controller->state_duration = 0;
    
    // 初始化传感器数据
    controller->sensor_data = 0;
    controller->last_sensor_data = 0;
    memset(controller->sensor_history, 0, sizeof(controller->sensor_history));
    controller->history_index = 0;
    
    // 初始化控制参数
    controller->left_speed = 0;
    controller->right_speed = 0;
    controller->base_speed = 5000;          // 基础速度
    controller->turn_speed_diff = 2000;     // 转弯速度差
    
    // 初始化丢线处理
    controller->line_lost_time = 0;
    controller->line_lost_timeout = 1000;   // 1秒丢线超时
    controller->last_valid_state = TRACK_STATE_STRAIGHT;
    controller->search_direction = 0;
    
    // 初始化性能统计
    controller->state_change_count = 0;
    controller->line_lost_count = 0;
    controller->total_run_time = 0;
    
    // 初始化配置参数
    controller->enable_adaptive = 1;        // 默认启用自适应
    controller->enable_pid = 1;             // 默认启用PID
    controller->debug_mode = 0;             // 默认关闭调试
}

/**
 * @brief 状态机主处理函数
 * @param controller 寻迹控制器指针
 * @param sensor_data 传感器数据
 */
void Track_StateMachine_Process(TrackController_t *controller, uint8_t sensor_data)
{
    // 更新传感器数据
    controller->last_sensor_data = controller->sensor_data;
    controller->sensor_data = Track_SensorFilter(controller, sensor_data);
    
    // 分析传感器数据，获取事件
    TrackEvent_t event = Track_Analyze_Sensor(controller->sensor_data);
    controller->last_event = event;
    
    // 更新状态持续时间
    uint32_t current_time = get_systicks();
    controller->state_duration = current_time - controller->state_enter_time;
    
    // 根据当前状态和事件进行状态转换
    TrackState_t new_state = controller->current_state;
    
    switch (controller->current_state) {
        case TRACK_STATE_IDLE:
            if (event == TRACK_EVENT_LINE_DETECTED || event == TRACK_EVENT_START_COMMAND) {
                new_state = TRACK_STATE_STRAIGHT;
            }
            break;
            
        case TRACK_STATE_STRAIGHT:
            if (event == TRACK_EVENT_LINE_LOST) {
                new_state = TRACK_STATE_LOST_LINE;
            } else if (event == TRACK_EVENT_LEFT_DEVIATION) {
                new_state = TRACK_STATE_LEFT_SLIGHT;
            } else if (event == TRACK_EVENT_RIGHT_DEVIATION) {
                new_state = TRACK_STATE_RIGHT_SLIGHT;
            } else if (event == TRACK_EVENT_STOP_COMMAND) {
                new_state = TRACK_STATE_STOP;
            }
            break;
            
        case TRACK_STATE_LEFT_SLIGHT:
            if (event == TRACK_EVENT_STRAIGHT_LINE) {
                new_state = TRACK_STATE_STRAIGHT;
            } else if (event == TRACK_EVENT_SHARP_TURN) {
                new_state = TRACK_STATE_LEFT_TURN;
            } else if (event == TRACK_EVENT_LINE_LOST) {
                new_state = TRACK_STATE_LOST_LINE;
            }
            break;
            
        case TRACK_STATE_RIGHT_SLIGHT:
            if (event == TRACK_EVENT_STRAIGHT_LINE) {
                new_state = TRACK_STATE_STRAIGHT;
            } else if (event == TRACK_EVENT_SHARP_TURN) {
                new_state = TRACK_STATE_RIGHT_TURN;
            } else if (event == TRACK_EVENT_LINE_LOST) {
                new_state = TRACK_STATE_LOST_LINE;
            }
            break;
            
        case TRACK_STATE_LEFT_TURN:
            if (event == TRACK_EVENT_STRAIGHT_LINE) {
                new_state = TRACK_STATE_STRAIGHT;
            } else if (event == TRACK_EVENT_LINE_LOST) {
                new_state = TRACK_STATE_LOST_LINE;
            }
            // 转弯超时检测
            if (controller->state_duration > 2000) {  // 2秒转弯超时
                new_state = TRACK_STATE_SEARCH;
            }
            break;
            
        case TRACK_STATE_RIGHT_TURN:
            if (event == TRACK_EVENT_STRAIGHT_LINE) {
                new_state = TRACK_STATE_STRAIGHT;
            } else if (event == TRACK_EVENT_LINE_LOST) {
                new_state = TRACK_STATE_LOST_LINE;
            }
            // 转弯超时检测
            if (controller->state_duration > 2000) {  // 2秒转弯超时
                new_state = TRACK_STATE_SEARCH;
            }
            break;
            
        case TRACK_STATE_LOST_LINE:
            if (event == TRACK_EVENT_LINE_DETECTED) {
                new_state = controller->last_valid_state;
            } else if (controller->state_duration > controller->line_lost_timeout) {
                new_state = TRACK_STATE_SEARCH;
                controller->line_lost_count++;
            }
            break;
            
        case TRACK_STATE_SEARCH:
            if (event == TRACK_EVENT_LINE_DETECTED) {
                new_state = TRACK_STATE_STRAIGHT;
            } else if (controller->state_duration > 5000) {  // 5秒搜索超时
                new_state = TRACK_STATE_STOP;
            }
            break;
            
        case TRACK_STATE_STOP:
            if (event == TRACK_EVENT_START_COMMAND) {
                new_state = TRACK_STATE_STRAIGHT;
            }
            break;
            
        default:
            new_state = TRACK_STATE_IDLE;
            break;
    }
    
    // 执行状态切换
    if (new_state != controller->current_state) {
        Track_ChangeState(controller, new_state);
    }
    
    // 执行当前状态的处理函数
    switch (controller->current_state) {
        case TRACK_STATE_STRAIGHT:
            Track_State_Straight(controller);
            break;
        case TRACK_STATE_LEFT_SLIGHT:
            Track_State_LeftTurn(controller, 0);
            break;
        case TRACK_STATE_RIGHT_SLIGHT:
            Track_State_RightTurn(controller, 0);
            break;
        case TRACK_STATE_LEFT_TURN:
            Track_State_LeftTurn(controller, 1);
            break;
        case TRACK_STATE_RIGHT_TURN:
            Track_State_RightTurn(controller, 1);
            break;
        case TRACK_STATE_LEFT_SHARP:
            Track_State_LeftTurn(controller, 2);
            break;
        case TRACK_STATE_RIGHT_SHARP:
            Track_State_RightTurn(controller, 2);
            break;
        case TRACK_STATE_LOST_LINE:
            Track_State_LostLine(controller);
            break;
        case TRACK_STATE_SEARCH:
            Track_State_Search(controller);
            break;
        case TRACK_STATE_STOP:
            Track_State_Stop(controller);
            break;
        default:
            Track_State_Stop(controller);
            break;
    }
}

/**************************** 传感器分析函数 ****************************/

/**
 * @brief 传感器数据分析
 * @param sensor_data 8位传感器数据
 * @return 检测到的事件
 */
TrackEvent_t Track_Analyze_Sensor(uint8_t sensor_data)
{
    // 计算激活的传感器数量
    uint8_t active_count = 0;
    for (int i = 0; i < 8; i++) {
        if (sensor_data & (1 << i)) {
            active_count++;
        }
    }

    // 无传感器激活 - 丢线
    if (active_count == 0) {
        return TRACK_EVENT_LINE_LOST;
    }

    // 计算线位置
    int16_t line_pos = Track_CalculateLinePosition(sensor_data);

    // 根据线位置判断事件
    if (line_pos >= -10 && line_pos <= 10) {
        return TRACK_EVENT_STRAIGHT_LINE;
    } else if (line_pos < -50) {
        return (line_pos < -80) ? TRACK_EVENT_SHARP_TURN : TRACK_EVENT_LEFT_DEVIATION;
    } else if (line_pos > 50) {
        return (line_pos > 80) ? TRACK_EVENT_SHARP_TURN : TRACK_EVENT_RIGHT_DEVIATION;
    }

    return TRACK_EVENT_LINE_DETECTED;
}

/**
 * @brief 计算线位置
 * @param sensor_data 传感器数据
 * @return 线位置 (-100到100, 0为中心)
 */
int16_t Track_CalculateLinePosition(uint8_t sensor_data)
{
    int32_t weighted_sum = 0;
    int32_t total_weight = 0;

    // 权重数组：从左到右 -100, -70, -40, -20, 20, 40, 70, 100
    int8_t weights[8] = {-100, -70, -40, -20, 20, 40, 70, 100};

    for (int i = 0; i < 8; i++) {
        if (sensor_data & (1 << i)) {
            weighted_sum += weights[i];
            total_weight += (weights[i] > 0) ? weights[i] : -weights[i];
        }
    }

    if (total_weight == 0) {
        return 0;  // 无有效数据
    }

    return (int16_t)(weighted_sum * 100 / total_weight);
}

/**
 * @brief 传感器数据滤波
 * @param controller 寻迹控制器指针
 * @param sensor_data 原始传感器数据
 * @return 滤波后的传感器数据
 */
uint8_t Track_SensorFilter(TrackController_t *controller, uint8_t sensor_data)
{
    // 更新历史数据
    controller->sensor_history[controller->history_index] = sensor_data;
    controller->history_index = (controller->history_index + 1) % 10;

    // 简单的多数表决滤波
    uint8_t filtered_data = 0;
    for (int bit = 0; bit < 8; bit++) {
        int count = 0;
        for (int i = 0; i < 10; i++) {
            if (controller->sensor_history[i] & (1 << bit)) {
                count++;
            }
        }
        if (count >= 5) {  // 超过一半的历史数据中该位为1
            filtered_data |= (1 << bit);
        }
    }

    return filtered_data;
}

/**************************** 状态处理函数实现 ****************************/

/**
 * @brief 直线行驶状态处理
 * @param controller 寻迹控制器指针
 */
void Track_State_Straight(TrackController_t *controller)
{
    // 计算线位置进行微调
    int16_t line_pos = Track_CalculateLinePosition(controller->sensor_data);

    // 基础速度
    controller->left_speed = controller->base_speed;
    controller->right_speed = controller->base_speed;

    // 根据线位置进行微调
    int32_t adjustment = line_pos * 10;  // 调整系数
    controller->left_speed -= adjustment;
    controller->right_speed += adjustment;

    // 自适应速度控制
    if (controller->enable_adaptive) {
        Track_AdaptiveSpeedControl(controller, line_pos);
    }

    // 应用PID控制
    if (controller->enable_pid) {
        controller->left_speed = Velocity_B(controller->left_speed, Get_Encoder_countB);
        controller->right_speed = Velocity_A(controller->right_speed, Get_Encoder_countA);
    }

    // 输出到电机
    Set_PWM(controller->right_speed, controller->left_speed);

    // 记录为有效状态
    controller->last_valid_state = TRACK_STATE_STRAIGHT;
}

/**
 * @brief 左转状态处理
 * @param controller 寻迹控制器指针
 * @param turn_level 转弯级别 (0=轻微, 1=普通, 2=急转)
 */
void Track_State_LeftTurn(TrackController_t *controller, uint8_t turn_level)
{
    // 根据转弯级别设置速度
    int32_t speed_reduction = controller->turn_speed_diff * (turn_level + 1);

    controller->left_speed = controller->base_speed - speed_reduction;
    controller->right_speed = controller->base_speed + speed_reduction / 2;

    // 确保速度不为负
    if (controller->left_speed < 500) controller->left_speed = 500;

    // 应用PID控制
    if (controller->enable_pid) {
        controller->left_speed = Velocity_B(controller->left_speed, Get_Encoder_countB);
        controller->right_speed = Velocity_A(controller->right_speed, Get_Encoder_countA);
    }

    // 输出到电机
    Set_PWM(controller->right_speed, controller->left_speed);

    // 记录为有效状态
    controller->last_valid_state = TRACK_STATE_LEFT_TURN;
}

/**
 * @brief 右转状态处理
 * @param controller 寻迹控制器指针
 * @param turn_level 转弯级别 (0=轻微, 1=普通, 2=急转)
 */
void Track_State_RightTurn(TrackController_t *controller, uint8_t turn_level)
{
    // 根据转弯级别设置速度
    int32_t speed_reduction = controller->turn_speed_diff * (turn_level + 1);

    controller->left_speed = controller->base_speed + speed_reduction / 2;
    controller->right_speed = controller->base_speed - speed_reduction;

    // 确保速度不为负
    if (controller->right_speed < 500) controller->right_speed = 500;

    // 应用PID控制
    if (controller->enable_pid) {
        controller->left_speed = Velocity_B(controller->left_speed, Get_Encoder_countB);
        controller->right_speed = Velocity_A(controller->right_speed, Get_Encoder_countA);
    }

    // 输出到电机
    Set_PWM(controller->right_speed, controller->left_speed);

    // 记录为有效状态
    controller->last_valid_state = TRACK_STATE_RIGHT_TURN;
}

/**
 * @brief 丢线状态处理
 * @param controller 寻迹控制器指针
 */
void Track_State_LostLine(TrackController_t *controller)
{
    // 根据最后有效状态决定处理策略
    if (controller->last_valid_state == TRACK_STATE_LEFT_TURN ||
        controller->last_valid_state == TRACK_STATE_LEFT_SLIGHT) {
        // 继续左转寻找线
        controller->left_speed = controller->base_speed / 2;
        controller->right_speed = controller->base_speed;
    } else if (controller->last_valid_state == TRACK_STATE_RIGHT_TURN ||
               controller->last_valid_state == TRACK_STATE_RIGHT_SLIGHT) {
        // 继续右转寻找线
        controller->left_speed = controller->base_speed;
        controller->right_speed = controller->base_speed / 2;
    } else {
        // 直线状态丢线，保持直行
        controller->left_speed = controller->base_speed / 2;
        controller->right_speed = controller->base_speed / 2;
    }

    // 输出到电机
    Set_PWM(controller->right_speed, controller->left_speed);
}

/**
 * @brief 搜索状态处理
 * @param controller 寻迹控制器指针
 */
void Track_State_Search(TrackController_t *controller)
{
    // 左右摆动搜索
    uint32_t search_cycle = (controller->state_duration / 500) % 4;  // 每500ms一个周期

    switch (search_cycle) {
        case 0:  // 左转搜索
            controller->left_speed = 1000;
            controller->right_speed = 4000;
            break;
        case 1:  // 右转搜索
            controller->left_speed = 4000;
            controller->right_speed = 1000;
            break;
        case 2:  // 左转搜索
            controller->left_speed = 1000;
            controller->right_speed = 4000;
            break;
        case 3:  // 右转搜索
            controller->left_speed = 4000;
            controller->right_speed = 1000;
            break;
    }

    // 输出到电机
    Set_PWM(controller->right_speed, controller->left_speed);
}

/**
 * @brief 停止状态处理
 * @param controller 寻迹控制器指针
 */
void Track_State_Stop(TrackController_t *controller)
{
    controller->left_speed = 0;
    controller->right_speed = 0;
    Set_PWM(0, 0);
}

/**************************** 辅助函数实现 ****************************/

/**
 * @brief 状态切换函数
 * @param controller 寻迹控制器指针
 * @param new_state 新状态
 */
void Track_ChangeState(TrackController_t *controller, TrackState_t new_state)
{
    controller->last_state = controller->current_state;
    controller->current_state = new_state;
    controller->state_enter_time = get_systicks();
    controller->state_duration = 0;
    controller->state_change_count++;
}

/**
 * @brief 自适应速度控制
 * @param controller 寻迹控制器指针
 * @param line_position 线位置
 */
void Track_AdaptiveSpeedControl(TrackController_t *controller, int16_t line_position)
{
    // 根据线位置偏差调整基础速度
    int16_t abs_pos = (line_position > 0) ? line_position : -line_position;

    if (abs_pos < 20) {
        // 线很直，可以加速
        controller->base_speed = 6000;
    } else if (abs_pos < 50) {
        // 轻微偏差，正常速度
        controller->base_speed = 5000;
    } else {
        // 偏差较大，减速
        controller->base_speed = 4000;
    }
}

/**************************** 配置和调试函数实现 ****************************/

/**
 * @brief 设置基础速度
 * @param speed 基础速度值
 */
void Track_SetBaseSpeed(int32_t speed)
{
    track_controller.base_speed = speed;
}

/**
 * @brief 设置转弯速度差
 * @param speed_diff 转弯速度差
 */
void Track_SetTurnSpeedDiff(int32_t speed_diff)
{
    track_controller.turn_speed_diff = speed_diff;
}

/**
 * @brief 使能自适应控制
 * @param enable 使能标志
 */
void Track_EnableAdaptive(uint8_t enable)
{
    track_controller.enable_adaptive = enable;
}

/**
 * @brief 使能PID控制
 * @param enable 使能标志
 */
void Track_EnablePID(uint8_t enable)
{
    track_controller.enable_pid = enable;
}

/**
 * @brief 获取寻迹状态信息
 * @param info_buffer 信息缓冲区
 * @param buffer_size 缓冲区大小
 */
void Track_GetStatusInfo(char *info_buffer, uint16_t buffer_size)
{
    snprintf(info_buffer, buffer_size,
        "State:%s Sensor:0x%02X Line:%d L:%d R:%d",
        state_names[track_controller.current_state],
        track_controller.sensor_data,
        Track_CalculateLinePosition(track_controller.sensor_data),
        track_controller.left_speed,
        track_controller.right_speed);
}

/**
 * @brief 获取性能统计
 * @param controller 寻迹控制器指针
 * @param stats_buffer 统计信息缓冲区
 * @param buffer_size 缓冲区大小
 */
void Track_GetPerformanceStats(TrackController_t *controller, char *stats_buffer, uint16_t buffer_size)
{
    uint32_t current_time = get_systicks();
    controller->total_run_time = current_time;

    snprintf(stats_buffer, buffer_size,
        "Runtime:%ds Changes:%d LostLine:%d",
        (int)(controller->total_run_time / 1000),
        (int)controller->state_change_count,
        (int)controller->line_lost_count);
}

/**
 * @brief 重置性能统计
 * @param controller 寻迹控制器指针
 */
void Track_ResetStats(TrackController_t *controller)
{
    controller->state_change_count = 0;
    controller->line_lost_count = 0;
    controller->total_run_time = get_systicks();
}

/**************************** 系统初始化函数 ****************************/

/**
 * @brief 寻迹系统初始化 - 自动初始化全局控制器
 * @note 在系统启动时调用，初始化状态机
 */
void Track_System_Init(void)
{
    Track_Controller_Init(&track_controller);
}
