#ifndef PID_CONTROLLER_H
#define PID_CONTROLLER_H

#include "bsp_system.h"

/**
 * @file pid_controller.h
 * @brief PID控制器头文件 - 基于移植文档最佳实践设计
 * @details 实现高精度PID控制算法，利用80MHz主频优势实现1kHz控制频率
 * <AUTHOR>
 */

/**************************** PID参数结构体定义 ****************************/

/**
 * @brief PID控制器参数结构体
 */
typedef struct {
    // PID参数
    float Kp;              // 比例系数
    float Ki;              // 积分系数  
    float Kd;              // 微分系数
    
    // 控制变量
    float target;          // 目标值
    float current;         // 当前值
    float error;           // 当前误差
    float last_error;      // 上次误差
    float integral;        // 积分累积
    float derivative;      // 微分值
    float output;          // 输出值
    
    // 限制参数
    float output_max;      // 输出最大值
    float output_min;      // 输出最小值
    float integral_max;    // 积分限幅最大值
    float integral_min;    // 积分限幅最小值
    
    // 时间参数
    uint32_t last_time;    // 上次计算时间(ms)
    float dt;              // 时间间隔(s)
    
    // 状态标志
    uint8_t enabled;       // PID使能标志
    uint8_t first_run;     // 首次运行标志
} PID_Controller_t;

/**************************** 电机PID控制器实例 ****************************/

// 电机A（右电机）PID控制器
extern PID_Controller_t motor_a_pid;

// 电机B（左电机）PID控制器  
extern PID_Controller_t motor_b_pid;

/**************************** PID控制器函数声明 ****************************/

/**
 * @brief PID控制器初始化
 * @param pid PID控制器指针
 * @param kp 比例系数
 * @param ki 积分系数
 * @param kd 微分系数
 */
void PID_Init(PID_Controller_t *pid, float kp, float ki, float kd);

/**
 * @brief 设置PID输出限制
 * @param pid PID控制器指针
 * @param min 输出最小值
 * @param max 输出最大值
 */
void PID_SetOutputLimits(PID_Controller_t *pid, float min, float max);

/**
 * @brief 设置PID积分限制
 * @param pid PID控制器指针
 * @param min 积分最小值
 * @param max 积分最大值
 */
void PID_SetIntegralLimits(PID_Controller_t *pid, float min, float max);

/**
 * @brief PID控制器计算
 * @param pid PID控制器指针
 * @param target 目标值
 * @param current 当前值
 * @return PID输出值
 */
float PID_Compute(PID_Controller_t *pid, float target, float current);

/**
 * @brief 重置PID控制器
 * @param pid PID控制器指针
 */
void PID_Reset(PID_Controller_t *pid);

/**
 * @brief 使能/禁用PID控制器
 * @param pid PID控制器指针
 * @param enable 使能标志 (1=使能, 0=禁用)
 */
void PID_SetEnable(PID_Controller_t *pid, uint8_t enable);

/**************************** 电机速度控制函数 ****************************/

/**
 * @brief 电机A速度控制（右电机）
 * @param target_speed 目标速度
 * @param current_encoder 当前编码器值
 * @return PWM输出值
 * @note 重新实现empty.c中被注释的Velocity_A函数
 */
int32_t Velocity_A(int32_t target_speed, int32_t current_encoder);

/**
 * @brief 电机B速度控制（左电机）
 * @param target_speed 目标速度  
 * @param current_encoder 当前编码器值
 * @return PWM输出值
 * @note 重新实现empty.c中被注释的Velocity_B函数
 */
int32_t Velocity_B(int32_t target_speed, int32_t current_encoder);

/**************************** PID参数调节函数 ****************************/

/**
 * @brief 设置电机A的PID参数
 * @param kp 比例系数
 * @param ki 积分系数
 * @param kd 微分系数
 */
void Motor_A_SetPID(float kp, float ki, float kd);

/**
 * @brief 设置电机B的PID参数
 * @param kp 比例系数
 * @param ki 积分系数
 * @param kd 微分系数
 */
void Motor_B_SetPID(float kp, float ki, float kd);

/**
 * @brief 获取电机A的PID状态
 * @param pid_status 返回PID状态结构体指针
 */
void Motor_A_GetPIDStatus(PID_Controller_t **pid_status);

/**
 * @brief 获取电机B的PID状态
 * @param pid_status 返回PID状态结构体指针
 */
void Motor_B_GetPIDStatus(PID_Controller_t **pid_status);

/**************************** 自适应PID函数 ****************************/

/**
 * @brief 自适应PID参数调节
 * @param pid PID控制器指针
 * @note 根据系统响应自动调节PID参数
 */
void PID_AdaptiveAdjust(PID_Controller_t *pid);

/**
 * @brief PID控制器性能评估
 * @param pid PID控制器指针
 * @return 性能指标 (0-100, 100为最佳)
 */
uint8_t PID_PerformanceEvaluation(PID_Controller_t *pid);

/**************************** 调试和监控函数 ****************************/

/**
 * @brief 获取PID调试信息
 * @param pid PID控制器指针
 * @param debug_info 调试信息字符串缓冲区
 * @param buffer_size 缓冲区大小
 */
void PID_GetDebugInfo(PID_Controller_t *pid, char *debug_info, uint16_t buffer_size);

/**
 * @brief PID控制器系统初始化
 * @note 初始化所有电机PID控制器
 */
void PID_System_Init(void);

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif

#endif /* PID_CONTROLLER_H */
