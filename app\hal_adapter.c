#include "hal_adapter.h"

/**
 * @file hal_adapter.c
 * @brief HAL适配层实现文件 - 基于STM32F103到MSPM0G3507移植文档4.2节设计
 * @details 封装MSPM0 SDK API，提供STM32风格的接口实现
 * <AUTHOR>
 */

// 全局错误状态
static HAL_StatusTypeDef g_hal_error_status = HAL_OK;

/**************************** PWM适配层实现 ****************************/

/**
 * @brief 设置右电机PWM比较值
 * @param compare PWM比较值 (0-20000)
 * @note 基于移植文档，封装DL_Timer_setCaptureCompareValue API
 */
void PWM_SetCompare1(uint16_t compare)
{
    // 右电机PWM控制 - 对应电机A
    DL_Timer_setCaptureCompareValue(PWM_0_INST, compare, GPIO_PWM_0_C0_IDX);
}

/**
 * @brief 设置左电机PWM比较值  
 * @param compare PWM比较值 (0-20000)
 * @note 基于移植文档，封装DL_Timer_setCaptureCompareValue API
 */
void PWM_SetCompare2(uint16_t compare)
{
    // 左电机PWM控制 - 对应电机B
    DL_Timer_setCaptureCompareValue(PWM_0_INST, compare, GPIO_PWM_0_C1_IDX);
}

/**
 * @brief PWM初始化
 * @note PWM配置已在SysConfig中完成，这里只需要启动
 */
void PWM_Init(void)
{
    // PWM定时器已在SYSCFG_DL_init()中初始化
    // 这里只需要确保定时器运行
    DL_Timer_startCounter(PWM_0_INST);
    g_hal_error_status = HAL_OK;
}

/**
 * @brief 第二路PWM初始化（兼容接口）
 * @note 与PWM_Init功能相同，保持接口兼容性
 */
void PWM2_Init(void)
{
    PWM_Init();
}

/**************************** ADC适配层实现 ****************************/

/**
 * @brief 获取ADC转换值
 * @return ADC转换结果 (0-4095, 12位ADC)
 * @note 基于移植文档，封装MSPM0 ADC API
 */
uint16_t ADC_GetValue(void)
{
    uint16_t adc_result = 0;
    
    // 启动ADC转换
    DL_ADC12_enableConversions(ADC12_0_INST);
    DL_ADC12_startConversion(ADC12_0_INST);
    
    // 等待转换完成
    while (DL_ADC12_isBusy(ADC12_0_INST)) {
        // 防止死循环，添加超时检测
        static uint32_t timeout_counter = 0;
        if (++timeout_counter > 10000) {
            g_hal_error_status = HAL_TIMEOUT;
            DL_ADC12_disableConversions(ADC12_0_INST);
            return 0;
        }
    }
    
    // 读取转换结果
    adc_result = DL_ADC12_getMemResult(ADC12_0_INST, DL_ADC12_MEM_IDX_0);
    
    // 停止ADC转换
    DL_ADC12_stopConversion(ADC12_0_INST);
    DL_ADC12_disableConversions(ADC12_0_INST);
    
    g_hal_error_status = HAL_OK;
    return adc_result;
}

/**
 * @brief ADC配置初始化
 * @note ADC配置已在SysConfig中完成
 */
void ADC_Config(void)
{
    // ADC配置已在SYSCFG_DL_init()中完成
    // 这里可以添加额外的配置
    g_hal_error_status = HAL_OK;
}

/**************************** 定时器适配层实现 ****************************/

/**
 * @brief 定时器初始化
 * @note 定时器配置已在SysConfig中完成
 */
void Timer_Init(void)
{
    // 定时器配置已在SYSCFG_DL_init()中完成
    // 确保定时器正常运行
    DL_TimerG_startCounter(TIMER_0_INST);
    g_hal_error_status = HAL_OK;
}

/**************************** 延时适配层实现 ****************************/

/**
 * @brief 毫秒级延时
 * @param ms 延时时间(毫秒)
 * @note 基于80MHz系统时钟计算延时周期
 */
void Delay_ms(uint32_t ms)
{
    // 80MHz时钟，1ms = 80000个时钟周期
    DL_Common_delayCycles(ms * 80000);
}

/**
 * @brief 微秒级延时
 * @param us 延时时间(微秒)  
 * @note 基于80MHz系统时钟计算延时周期
 */
void Delay_us(uint32_t us)
{
    // 80MHz时钟，1us = 80个时钟周期
    DL_Common_delayCycles(us * 80);
}

/**************************** 系统适配层实现 ****************************/

/**
 * @brief 系统初始化
 * @note 调用SysConfig生成的初始化函数
 */
void System_Init(void)
{
    SYSCFG_DL_init();
    g_hal_error_status = HAL_OK;
}

/**
 * @brief 系统复位
 * @note 软件复位系统
 */
void System_Reset(void)
{
    NVIC_SystemReset();
}

/**************************** 中断适配层实现 ****************************/

/**
 * @brief 中断配置
 * @param IRQn 中断号
 * @param priority 中断优先级 (0-3, 适应M0+的4级优先级)
 * @note 基于移植文档，适应M0+中断优先级限制
 */
void NVIC_Config(uint32_t IRQn, uint32_t priority)
{
    // 确保优先级在M0+支持范围内 (0-3)
    if (priority > 3) {
        priority = 3;
        g_hal_error_status = HAL_ERROR;
    }
    
    NVIC_SetPriority((IRQn_Type)IRQn, priority);
    g_hal_error_status = HAL_OK;
}

/**
 * @brief 使能中断
 * @param IRQn 中断号
 */
void NVIC_Enable(uint32_t IRQn)
{
    NVIC_EnableIRQ((IRQn_Type)IRQn);
    g_hal_error_status = HAL_OK;
}

/**
 * @brief 禁用中断
 * @param IRQn 中断号
 */
void NVIC_Disable(uint32_t IRQn)
{
    NVIC_DisableIRQ((IRQn_Type)IRQn);
    g_hal_error_status = HAL_OK;
}

/**************************** 错误处理实现 ****************************/

/**
 * @brief 获取最后一次错误状态
 * @return HAL错误状态
 */
HAL_StatusTypeDef HAL_GetLastError(void)
{
    return g_hal_error_status;
}

/**
 * @brief 清除错误状态
 */
void HAL_ClearError(void)
{
    g_hal_error_status = HAL_OK;
}
