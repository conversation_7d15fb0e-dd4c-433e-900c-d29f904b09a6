#include "pid_controller.h"

/**
 * @file pid_controller.c
 * @brief PID控制器实现文件 - 基于移植文档最佳实践
 * @details 实现高精度PID控制算法，重新启用empty.c中被注释的PID功能
 * <AUTHOR>
 */

/**************************** 全局变量定义 ****************************/

// 电机A（右电机）PID控制器实例
PID_Controller_t motor_a_pid;

// 电机B（左电机）PID控制器实例
PID_Controller_t motor_b_pid;

// 速度计算相关变量
static int32_t last_encoder_a = 0;
static int32_t last_encoder_b = 0;
static uint32_t last_calc_time = 0;

/**************************** PID控制器基础函数 ****************************/

/**
 * @brief PID控制器初始化
 * @param pid PID控制器指针
 * @param kp 比例系数
 * @param ki 积分系数
 * @param kd 微分系数
 */
void PID_Init(PID_Controller_t *pid, float kp, float ki, float kd)
{
    // 设置PID参数
    pid->Kp = kp;
    pid->Ki = ki;
    pid->Kd = kd;
    
    // 初始化控制变量
    pid->target = 0.0f;
    pid->current = 0.0f;
    pid->error = 0.0f;
    pid->last_error = 0.0f;
    pid->integral = 0.0f;
    pid->derivative = 0.0f;
    pid->output = 0.0f;
    
    // 设置默认限制
    pid->output_max = 10000.0f;   // 最大PWM值
    pid->output_min = -10000.0f;  // 最小PWM值
    pid->integral_max = 5000.0f;  // 积分限幅
    pid->integral_min = -5000.0f;
    
    // 初始化时间参数
    pid->last_time = get_systicks();
    pid->dt = 0.01f;  // 默认10ms控制周期
    
    // 初始化状态标志
    pid->enabled = 1;
    pid->first_run = 1;
}

/**
 * @brief 设置PID输出限制
 * @param pid PID控制器指针
 * @param min 输出最小值
 * @param max 输出最大值
 */
void PID_SetOutputLimits(PID_Controller_t *pid, float min, float max)
{
    pid->output_min = min;
    pid->output_max = max;
    
    // 限制当前输出值
    if (pid->output > max) pid->output = max;
    if (pid->output < min) pid->output = min;
}

/**
 * @brief 设置PID积分限制
 * @param pid PID控制器指针
 * @param min 积分最小值
 * @param max 积分最大值
 */
void PID_SetIntegralLimits(PID_Controller_t *pid, float min, float max)
{
    pid->integral_min = min;
    pid->integral_max = max;
    
    // 限制当前积分值
    if (pid->integral > max) pid->integral = max;
    if (pid->integral < min) pid->integral = min;
}

/**
 * @brief PID控制器计算
 * @param pid PID控制器指针
 * @param target 目标值
 * @param current 当前值
 * @return PID输出值
 */
float PID_Compute(PID_Controller_t *pid, float target, float current)
{
    if (!pid->enabled) {
        return 0.0f;
    }
    
    // 获取当前时间并计算时间间隔
    uint32_t current_time = get_systicks();
    
    if (!pid->first_run) {
        pid->dt = (float)(current_time - pid->last_time) / 1000.0f;  // 转换为秒
        
        // 防止时间间隔过小或过大
        if (pid->dt < 0.001f) pid->dt = 0.001f;  // 最小1ms
        if (pid->dt > 0.1f) pid->dt = 0.1f;      // 最大100ms
    } else {
        pid->first_run = 0;
        pid->dt = 0.01f;  // 首次运行使用默认值
    }
    
    pid->last_time = current_time;
    
    // 更新目标值和当前值
    pid->target = target;
    pid->current = current;
    
    // 计算误差
    pid->error = target - current;
    
    // 计算积分项（梯形积分）
    pid->integral += (pid->error + pid->last_error) * pid->dt * 0.5f;
    
    // 积分限幅
    if (pid->integral > pid->integral_max) pid->integral = pid->integral_max;
    if (pid->integral < pid->integral_min) pid->integral = pid->integral_min;
    
    // 计算微分项
    pid->derivative = (pid->error - pid->last_error) / pid->dt;
    
    // 计算PID输出
    pid->output = pid->Kp * pid->error + 
                  pid->Ki * pid->integral + 
                  pid->Kd * pid->derivative;
    
    // 输出限幅
    if (pid->output > pid->output_max) pid->output = pid->output_max;
    if (pid->output < pid->output_min) pid->output = pid->output_min;
    
    // 保存当前误差为下次计算的上次误差
    pid->last_error = pid->error;
    
    return pid->output;
}

/**
 * @brief 重置PID控制器
 * @param pid PID控制器指针
 */
void PID_Reset(PID_Controller_t *pid)
{
    pid->error = 0.0f;
    pid->last_error = 0.0f;
    pid->integral = 0.0f;
    pid->derivative = 0.0f;
    pid->output = 0.0f;
    pid->first_run = 1;
    pid->last_time = get_systicks();
}

/**
 * @brief 使能/禁用PID控制器
 * @param pid PID控制器指针
 * @param enable 使能标志 (1=使能, 0=禁用)
 */
void PID_SetEnable(PID_Controller_t *pid, uint8_t enable)
{
    if (enable && !pid->enabled) {
        // 从禁用变为使能时，重置PID状态
        PID_Reset(pid);
    }
    pid->enabled = enable;
}

/**************************** 电机速度控制函数 ****************************/

/**
 * @brief 计算电机速度（基于编码器）
 * @param current_encoder 当前编码器值
 * @param last_encoder 上次编码器值指针
 * @return 电机速度 (脉冲/秒)
 */
static float Calculate_Motor_Speed(int32_t current_encoder, int32_t *last_encoder)
{
    uint32_t current_time = get_systicks();
    static uint32_t last_speed_calc_time = 0;
    
    if (last_speed_calc_time == 0) {
        last_speed_calc_time = current_time;
        *last_encoder = current_encoder;
        return 0.0f;
    }
    
    float dt = (float)(current_time - last_speed_calc_time) / 1000.0f;  // 转换为秒
    if (dt < 0.001f) return 0.0f;  // 时间间隔太小，返回0
    
    float speed = (float)(current_encoder - *last_encoder) / dt;
    
    *last_encoder = current_encoder;
    last_speed_calc_time = current_time;
    
    return speed;
}

/**
 * @brief 电机A速度控制（右电机）
 * @param target_speed 目标速度
 * @param current_encoder 当前编码器值
 * @return PWM输出值
 * @note 重新实现empty.c中被注释的Velocity_A函数
 */
int32_t Velocity_A(int32_t target_speed, int32_t current_encoder)
{
    // 计算当前速度
    float current_speed = Calculate_Motor_Speed(current_encoder, &last_encoder_a);
    
    // PID控制计算
    float pid_output = PID_Compute(&motor_a_pid, (float)target_speed, current_speed);
    
    return (int32_t)pid_output;
}

/**
 * @brief 电机B速度控制（左电机）
 * @param target_speed 目标速度
 * @param current_encoder 当前编码器值
 * @return PWM输出值
 * @note 重新实现empty.c中被注释的Velocity_B函数
 */
int32_t Velocity_B(int32_t target_speed, int32_t current_encoder)
{
    // 计算当前速度
    float current_speed = Calculate_Motor_Speed(current_encoder, &last_encoder_b);
    
    // PID控制计算
    float pid_output = PID_Compute(&motor_b_pid, (float)target_speed, current_speed);
    
    return (int32_t)pid_output;
}

/**************************** PID参数调节函数 ****************************/

/**
 * @brief 设置电机A的PID参数
 * @param kp 比例系数
 * @param ki 积分系数
 * @param kd 微分系数
 */
void Motor_A_SetPID(float kp, float ki, float kd)
{
    motor_a_pid.Kp = kp;
    motor_a_pid.Ki = ki;
    motor_a_pid.Kd = kd;
    PID_Reset(&motor_a_pid);  // 重置PID状态
}

/**
 * @brief 设置电机B的PID参数
 * @param kp 比例系数
 * @param ki 积分系数
 * @param kd 微分系数
 */
void Motor_B_SetPID(float kp, float ki, float kd)
{
    motor_b_pid.Kp = kp;
    motor_b_pid.Ki = ki;
    motor_b_pid.Kd = kd;
    PID_Reset(&motor_b_pid);  // 重置PID状态
}

/**
 * @brief 获取电机A的PID状态
 * @param pid_status 返回PID状态结构体指针
 */
void Motor_A_GetPIDStatus(PID_Controller_t **pid_status)
{
    *pid_status = &motor_a_pid;
}

/**
 * @brief 获取电机B的PID状态
 * @param pid_status 返回PID状态结构体指针
 */
void Motor_B_GetPIDStatus(PID_Controller_t **pid_status)
{
    *pid_status = &motor_b_pid;
}

/**************************** 自适应PID函数 ****************************/

/**
 * @brief 自适应PID参数调节
 * @param pid PID控制器指针
 * @note 根据系统响应自动调节PID参数
 */
void PID_AdaptiveAdjust(PID_Controller_t *pid)
{
    // 简单的自适应算法：根据误差大小调节参数
    float abs_error = (pid->error > 0) ? pid->error : -pid->error;

    if (abs_error > 100.0f) {
        // 误差较大时，增加比例系数，减少积分系数
        pid->Kp *= 1.1f;
        pid->Ki *= 0.9f;
    } else if (abs_error < 10.0f) {
        // 误差较小时，减少比例系数，增加积分系数
        pid->Kp *= 0.95f;
        pid->Ki *= 1.05f;
    }

    // 限制参数范围
    if (pid->Kp > 10.0f) pid->Kp = 10.0f;
    if (pid->Kp < 0.1f) pid->Kp = 0.1f;
    if (pid->Ki > 5.0f) pid->Ki = 5.0f;
    if (pid->Ki < 0.01f) pid->Ki = 0.01f;
}

/**
 * @brief PID控制器性能评估
 * @param pid PID控制器指针
 * @return 性能指标 (0-100, 100为最佳)
 */
uint8_t PID_PerformanceEvaluation(PID_Controller_t *pid)
{
    float abs_error = (pid->error > 0) ? pid->error : -pid->error;
    float target_abs = (pid->target > 0) ? pid->target : -pid->target;

    if (target_abs < 1.0f) return 100;  // 目标值太小，认为性能最佳

    float error_ratio = abs_error / target_abs;

    if (error_ratio < 0.01f) return 100;      // 误差小于1%
    else if (error_ratio < 0.05f) return 90;  // 误差小于5%
    else if (error_ratio < 0.1f) return 80;   // 误差小于10%
    else if (error_ratio < 0.2f) return 60;   // 误差小于20%
    else return 30;                           // 误差较大
}

/**************************** 调试和监控函数 ****************************/

/**
 * @brief 获取PID调试信息
 * @param pid PID控制器指针
 * @param debug_info 调试信息字符串缓冲区
 * @param buffer_size 缓冲区大小
 */
void PID_GetDebugInfo(PID_Controller_t *pid, char *debug_info, uint16_t buffer_size)
{
    snprintf(debug_info, buffer_size,
        "PID: T=%.1f C=%.1f E=%.1f O=%.1f P=%.3f I=%.3f D=%.3f",
        pid->target, pid->current, pid->error, pid->output,
        pid->Kp, pid->Ki, pid->Kd);
}

/**
 * @brief PID控制器系统初始化
 * @note 初始化所有电机PID控制器
 */
void PID_System_Init(void)
{
    // 初始化电机A PID控制器（右电机）
    // 基于经验值设置初始PID参数
    PID_Init(&motor_a_pid, 2.0f, 0.5f, 0.1f);
    PID_SetOutputLimits(&motor_a_pid, -8000.0f, 8000.0f);
    PID_SetIntegralLimits(&motor_a_pid, -3000.0f, 3000.0f);

    // 初始化电机B PID控制器（左电机）
    PID_Init(&motor_b_pid, 2.0f, 0.5f, 0.1f);
    PID_SetOutputLimits(&motor_b_pid, -8000.0f, 8000.0f);
    PID_SetIntegralLimits(&motor_b_pid, -3000.0f, 3000.0f);

    // 重置速度计算相关变量
    last_encoder_a = 0;
    last_encoder_b = 0;
    last_calc_time = get_systicks();
}
