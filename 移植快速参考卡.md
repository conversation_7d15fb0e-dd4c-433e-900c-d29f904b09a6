# STM32F103到MSPM0G3507移植快速参考卡

## 核心差异速查表

| 项目 | STM32F103C8 | MSPM0G3507 | 关键注意事项 |
|------|-------------|------------|--------------|
| **内核** | ARM Cortex-M3 | ARM Cortex-M0+ | 指令集简化，中断优先级减少 |
| **主频** | 72MHz | 80MHz | 时钟配置需调整 |
| **Flash** | 64KB | 128KB | 存储空间翻倍 |
| **SRAM** | 20KB | 32KB | 内存充足 |
| **中断优先级** | 16级(0-15) | 4级(0-3) | 重新分配优先级 |
| **开发环境** | Keil uVision | Code Composer Studio | 项目配置完全不同 |

## 关键API映射速查

### GPIO操作
```c
// STM32F103
GPIO_SetBits(GPIOB, GPIO_Pin_0);
GPIO_ResetBits(GPIOB, GPIO_Pin_0);
GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_0);

// MSPM0G3507
DL_GPIO_setPins(GPIO_PORT, GPIO_PIN);
DL_GPIO_clearPins(GPIO_PORT, GPIO_PIN);
DL_GPIO_readPins(GPIO_PORT, GPIO_PIN);
```

### PWM控制
```c
// STM32F103
TIM_SetCompare1(TIM3, compare_value);

// MSPM0G3507
DL_TimerG_setCaptureCompareValue(TIMER_INST, compare_value, DL_TIMER_CC_0_INDEX);
```

### ADC读取
```c
// STM32F103
ADC_SoftwareStartConvCmd(ADC1, ENABLE);
while(!ADC_GetFlagStatus(ADC1, ADC_FLAG_EOC));
result = ADC_GetConversionValue(ADC1);

// MSPM0G3507
DL_ADC12_startConversion(ADC12_0_INST);
while(DL_ADC12_isBusy(ADC12_0_INST));
result = DL_ADC12_getMemResult(ADC12_0_INST, DL_ADC12_MEM_IDX_0);
```

### 中断配置
```c
// STM32F103
NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
NVIC_Init(&NVIC_InitStructure);

// MSPM0G3507
NVIC_SetPriority(IRQn, 1);  // 0-3级优先级
NVIC_EnableIRQ(IRQn);
```

## 引脚重新分配速查

| 功能 | STM32F103 | MSPM0G3507 | 复用功能 |
|------|-----------|------------|----------|
| 左电机方向 | PB0, PB1 | PA0, PA1 | GPIO |
| 左电机PWM | PB6 | PA2 | TIMG0_CCP0 |
| 右电机方向 | PA4, PA5 | PA3, PA4 | GPIO |
| 右电机PWM | PA6 | PA5 | TIMG0_CCP1 |
| 右编码器 | PA8, PA9 | PA6, PA7 | TIMG1_CCP0/1 |
| 左编码器 | PA0, PA1 | PA8, PA9 | GPIO中断 |
| 传感器ADC | PA2 | PA10 | ADC0_CH5 |
| 传感器地址线 | PB12,PB13,PB14 | PA11,PA12,PA13 | GPIO |
| OLED I2C | PB8, PB9 | PA14, PA15 | I2C0_SCL/SDA |

## 时钟配置速查

### PWM频率计算
```c
// STM32F103: 50Hz PWM
// 72MHz / 72 / 20000 = 50Hz

// MSPM0G3507: 50Hz PWM  
// 80MHz / 80 / 20000 = 50Hz
// 需要调整分频器从72改为80
```

### 延时函数转换
```c
// STM32F103
Delay_ms(1000);

// MSPM0G3507
DL_Common_delayCycles(80000000);  // 1秒 @ 80MHz
// 或使用适配函数
void Delay_ms(uint32_t ms) {
    DL_Common_delayCycles(ms * 80000);
}
```

## 中断处理转换模板

### 定时器中断
```c
// STM32F103
void TIM2_IRQHandler(void) {
    if (TIM_GetITStatus(TIM2, TIM_IT_Update) == SET) {
        // 处理代码
        TIM_ClearITPendingBit(TIM2, TIM_IT_Update);
    }
}

// MSPM0G3507
void TIMER_1S_INST_IRQHandler(void) {
    switch (DL_TimerG_getPendingInterrupt(TIMER_1S_INST)) {
        case DL_TIMER_IIDX_ZERO:
            // 处理代码
            break;
        default:
            break;
    }
}
```

### 外部中断
```c
// STM32F103
void EXTI0_IRQHandler(void) {
    if(EXTI_GetITStatus(EXTI_Line0) != RESET) {
        // 处理代码
        EXTI_ClearITPendingBit(EXTI_Line0);
    }
}

// MSPM0G3507
void GROUP1_IRQHandler(void) {
    switch (DL_Interrupt_getPendingGroup(DL_INTERRUPT_GROUP_1)) {
        case GPIO_ENCODER_INT_IIDX:
            // 处理代码
            DL_GPIO_clearInterruptStatus(GPIO_ENCODER_PORT, GPIO_ENCODER_PIN);
            break;
        default:
            break;
    }
}
```

## 适配层模板

### 头文件适配 (hal_adapter.h)
```c
#ifndef HAL_ADAPTER_H
#define HAL_ADAPTER_H

#include "ti_msp_dl_config.h"

// GPIO适配
#define GPIO_SetBits(port, pin)    DL_GPIO_setPins(port, pin)
#define GPIO_ResetBits(port, pin)  DL_GPIO_clearPins(port, pin)

// PWM适配
void PWM_SetCompare1(uint16_t compare);
void PWM_SetCompare2(uint16_t compare);

// ADC适配
uint16_t ADC_GetValue(void);

// 延时适配
void Delay_ms(uint32_t ms);

#endif
```

### 实现文件适配 (hal_adapter.c)
```c
#include "hal_adapter.h"

void PWM_SetCompare1(uint16_t compare) {
    DL_TimerG_setCaptureCompareValue(PWM_RIGHT_INST, compare, DL_TIMER_CC_0_INDEX);
}

void PWM_SetCompare2(uint16_t compare) {
    DL_TimerG_setCaptureCompareValue(PWM_LEFT_INST, compare, DL_TIMER_CC_0_INDEX);
}

uint16_t ADC_GetValue(void) {
    DL_ADC12_startConversion(ADC12_0_INST);
    while (DL_ADC12_isBusy(ADC12_0_INST));
    return DL_ADC12_getMemResult(ADC12_0_INST, DL_ADC12_MEM_IDX_0);
}

void Delay_ms(uint32_t ms) {
    DL_Common_delayCycles(ms * 80000);
}
```

## 常见错误速查

| 错误现象 | 可能原因 | 快速解决 |
|----------|----------|----------|
| 编译错误：未定义函数 | API名称不匹配 | 使用适配层或替换API |
| PWM频率不对 | 时钟分频参数错误 | 调整分频器：72→80 |
| 中断不响应 | 优先级超出范围 | 优先级改为0-3 |
| ADC读取异常 | 通道或参考电压错误 | 检查SysConfig配置 |
| I2C通信失败 | 时序或引脚错误 | 检查上拉电阻和时钟 |
| 系统复位 | 栈溢出或看门狗 | 增加栈大小，禁用看门狗 |

## 验证测试速查

### 基础功能测试
```c
// GPIO测试
void test_gpio(void) {
    DL_GPIO_setPins(TEST_PORT, TEST_PIN);
    Delay_ms(500);
    DL_GPIO_clearPins(TEST_PORT, TEST_PIN);
}

// PWM测试
void test_pwm(void) {
    PWM_SetCompare1(1000);  // 5%占空比
    Delay_ms(1000);
    PWM_SetCompare1(10000); // 50%占空比
}

// ADC测试
void test_adc(void) {
    uint16_t value = ADC_GetValue();
    printf("ADC值: %d\n", value);
}
```

## 性能对比参考

| 指标 | STM32F103 | MSPM0G3507 | 提升 |
|------|-----------|------------|------|
| 主频 | 72MHz | 80MHz | +11% |
| Flash | 64KB | 128KB | +100% |
| SRAM | 20KB | 32KB | +60% |
| ADC精度 | 12位 | 12位 | 相同 |
| PWM分辨率 | 16位 | 16位 | 相同 |

## 移植完成检查

- [ ] 所有模块编译通过
- [ ] 基础功能测试通过
- [ ] 寻迹算法工作正常
- [ ] 性能达到预期
- [ ] 稳定性测试通过
- [ ] 文档更新完成

## 紧急问题联系

如果遇到无法解决的问题：
1. 检查TI官方文档和示例代码
2. 查看MSPM0 SDK用户指南
3. 参考CCS调试指南
4. 使用示波器和逻辑分析仪调试硬件
5. 在TI E2E论坛寻求帮助

---
**提示**：保持原有算法逻辑不变，只替换底层硬件接口，这是移植成功的关键！
