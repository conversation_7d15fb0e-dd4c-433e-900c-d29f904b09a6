#ifndef HAL_ADAPTER_H
#define HAL_ADAPTER_H

#include "ti_msp_dl_config.h"

/**
 * @file hal_adapter.h
 * @brief HAL适配层头文件 - 基于STM32F103到MSPM0G3507移植文档4.2节设计
 * @details 提供STM32风格的API接口，封装MSPM0 SDK底层API，提升代码可移植性
 * <AUTHOR>
 */

/**************************** GPIO适配层定义 ****************************/
// 引脚定义适配 - 映射到实际硬件引脚
#define GPIO_Pin_0    AIN_AIN1_PIN      // 电机A方向控制1
#define GPIO_Pin_1    AIN_AIN2_PIN      // 电机A方向控制2  
#define GPIO_Pin_4    BIN_BIN1_PIN      // 电机B方向控制1
#define GPIO_Pin_5    BIN_BIN2_PIN      // 电机B方向控制2

// 端口定义适配
#define GPIOA         AIN_PORT          // 电机A控制端口
#define GPIOB         BIN_PORT          // 电机B控制端口

// GPIO操作适配宏 - 提供STM32风格接口
#define GPIO_SetBits(port, pin)         DL_GPIO_setPins(port, pin)
#define GPIO_ResetBits(port, pin)       DL_GPIO_clearPins(port, pin)
#define GPIO_ReadInputDataBit(port, pin) DL_GPIO_readPins(port, pin)

/**************************** PWM适配层定义 ****************************/
// PWM控制适配函数声明
void PWM_SetCompare1(uint16_t compare);    // 右电机PWM控制
void PWM_SetCompare2(uint16_t compare);    // 左电机PWM控制
void PWM_Init(void);                       // PWM初始化
void PWM2_Init(void);                      // 第二路PWM初始化

/**************************** ADC适配层定义 ****************************/
// ADC操作适配函数声明
uint16_t ADC_GetValue(void);               // 获取ADC转换值
void ADC_Config(void);                     // ADC配置初始化

/**************************** 定时器适配层定义 ****************************/
// 定时器操作适配函数声明
void Timer_Init(void);                     // 定时器初始化

/**************************** 延时适配层定义 ****************************/
// 延时函数适配 - 基于80MHz系统时钟
void Delay_ms(uint32_t ms);                // 毫秒级延时
void Delay_us(uint32_t us);                // 微秒级延时

/**************************** 系统适配层定义 ****************************/
// 系统控制适配函数声明
void System_Init(void);                    // 系统初始化
void System_Reset(void);                   // 系统复位

/**************************** 中断适配层定义 ****************************/
// 中断控制适配 - 适应M0+的4级优先级
void NVIC_Config(uint32_t IRQn, uint32_t priority);  // 中断配置
void NVIC_Enable(uint32_t IRQn);                     // 使能中断
void NVIC_Disable(uint32_t IRQn);                    // 禁用中断

/**************************** 错误处理定义 ****************************/
// 错误代码定义
typedef enum {
    HAL_OK = 0,
    HAL_ERROR,
    HAL_BUSY,
    HAL_TIMEOUT
} HAL_StatusTypeDef;

// 错误处理函数
HAL_StatusTypeDef HAL_GetLastError(void);
void HAL_ClearError(void);

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif

#endif /* HAL_ADAPTER_H */
